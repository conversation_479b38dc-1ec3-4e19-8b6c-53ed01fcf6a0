:root {
	--base-dark: hsl(198, 95%, 40%);
	--base-light: hsl(198, 48%, 95%);
	--base: hsl(196, 86%, 48%);
	--benefit-bg: hsla(30, 30%, 96%, 0.271);
	--bg-banner: hsla(0, 0%, 47%, 0.271);
	--card-header-bg: hsl(197, 27%, 90%);
	--card-text: hsl(0, 0%, 48%);
	--card: hsl(240, 20%, 99%);
	--container-gap: clamp(4rem, 4.618vw + 3.076rem, 8rem);
	--dim-text: hsl(200, 7%, 56%);
	--filters-active: hsl(197, 97%, 86%);
	--filters: hsl(0, 0%, 65%);
	--footer-heads: hsl(0, 0%, 15%);
	--footer-texts: hsl(226, 5%, 50%);
	--footer: hsl(200, 94%, 94%);
	--hamburger: hsl(30, 30%, 96%);
	--header-height: 4.8rem;
	--heading: hsl(0, 0%, 5%);
	--icon: hsl(0, 0%, 15%);
	--metric-card: hsl(240, 8%, 12%);
	--subheading: hsl(240, 6%, 10%);
	--text: hsl(0, 0%, 20%);
	--white: hsl(0, 0%, 100%);

	/* fonts */
	--text-s: clamp(0.875rem, 0.144vw + 0.846rem, 1rem);
	--text-m: clamp(1rem, 0.289vw + 0.942rem, 1.25rem);
	--text-l: clamp(1.25rem, 0.289vw + 1.192rem, 1.5rem);
	--text-xl: clamp(1.5rem, 0.722vw + 1.356rem, 2.125rem);
}

*,
*::before,
*::after {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

body {
	font-family: 'Manrope', sans-serif !important;
	font-optical-sizing: auto;
	font-weight: 400;
	font-size: var(--text-s);
	font-style: normal;
	background-color: var(--base-light);
	display: flex;
	flex-direction: column;
	min-height: 100dvh;
	position: relative;
	line-height: 1.6 !important;
	letter-spacing: 0.01em !important;
}

body h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
a {
	font-family: 'Manrope', sans-serif !important;
	font-optical-sizing: auto;
	margin: 0;
	padding: 0;
}

body header {
	position: sticky;
	top: 0;
	left: 0;
	margin-block: 2rem;
	z-index: 20;
}

body main {
	flex: 1;
}

body footer {
	margin-top: auto;
}

ul,
li {
	list-style: none;
}

a {
	text-decoration: none;
	color: inherit;
}

button,
a {
	cursor: pointer;
}

a:hover {
	color: inherit;
}

section {
	display: flex;
	flex-direction: column;
	gap: 0;
}

.container {
	width: 100% !important;
	max-inline-size: 106.5rem !important;
	margin-inline: auto;
	padding-inline: 1rem;
}

main .container:first-of-type {
	margin-top: var(--header-height);
}

main .container:not(:first-of-type) {
	margin-block: var(--container-gap);
}

.hidden {
	display: none;
}

.header {
	align-items: center;
	margin-block: 2rem;
	gap: 4rem;
}

#hamburger {
	aspect-ratio: 1;
	background: var(--hamburger);
	border-radius: 4px;
	border: 0;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	gap: 5px;
	height: 3rem;
	justify-content: center;
	padding: 5px;
	width: 3rem;
	transition: background 0.3s ease;

	&:hover,
	&:focus {
		background: var(--hamburger);
	}

	span {
		background: var(--subheading);
		border-radius: 50vw;
		display: flex;
		height: 2px;
		transition-duration: 0.3s;
		transition-property: rotate, scale, translate, width;
		transition-timing-function: ease-in-out;
		width: 100%;
	}

	&[aria-expanded='false'] {
		span:nth-child(3) {
			width: 50%;
		}
	}

	&[aria-expanded='true'] {
		span:nth-child(1) {
			rotate: 48deg;
			translate: 0 8px;
		}

		span:nth-child(2) {
			scale: 0;
		}

		span:nth-child(3) {
			rotate: -48deg;
			translate: 0 -8px;
		}
	}
}

.header-nav {
	opacity: 1;
	visibility: visible;
	transition: opacity 0.3s ease, visibility 0.3s ease;
}

.header-x,
.header-nav {
	width: fit-content;
	min-height: var(--header-height);
}

.logo_wrapper {
	background: var(--white) !important;
}

body.nav-open {
	background: var(--white) !important;
	overflow: hidden;
}

body.nav-open .header-nav {
	opacity: 0;
	visibility: hidden;
	pointer-events: none;
}

body.nav-open .logo_wrapper {
	background: none !important;
}

body.nav-open #hamburger {
	background: var(--base-light);
}

/* Mega Menu */
#mega-menu-id {
	--mm-line: #eee;

	display: none;
}

body.nav-open #mega-menu-id {
	display: block;
	position: fixed;
	left: 0;
	top: calc(var(--header-height) * 2);
	width: 100vw;
	height: calc(100vh - calc(var(--header-height) * 2));
	overflow-y: auto; /* Allow scrolling within the mega menu */
	z-index: 1000; /* Ensure it's above other content */
}

.mm-container {
	max-width: 100%;
	min-height: 100%;
	background: var(--white);
	border-top: 1px solid var(--mm-line);
	padding: 1rem 2rem;
}

.mega-menu-breadcrumb {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	margin-inline: auto;
	max-width: 106.5rem;
	font-size: 0.9rem;
	color: var(--footer-texts);
	padding-bottom: 1rem;
	border-bottom: 1px solid var(--mm-line);
}

.mega-menu-breadcrumb a {
	color: var(--base);
	font-weight: 600;
	text-decoration: none;
	cursor: pointer;
}

.mega-menu-breadcrumb a:hover {
	text-decoration: underline;
}

.mega-menu-breadcrumb span.current {
	font-weight: 600;
	color: #000;
}

.mm-wrapper {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 2rem;
	max-width: 106.5rem;
	margin-inline: auto;
	height: 100%;
	align-items: stretch;
	justify-content: stretch;
}

.mm-wrapper.no-children {
	grid-template-columns: 1fr 1fr 1fr;
}

.mm-wrapper.no-children #children-column {
	display: none;
}

.mm-wrapper.no-children #overview-column {
	grid-column: 2;
}

@media (max-width: 1024px) {
	.mm-wrapper,
	.mm-wrapper.no-children {
		grid-template-columns: 1fr;
	}

	.mm-wrapper.no-children #overview-column {
		grid-column: 1;
	}

	.mm-wrapper.no-children #children-column {
		display: block;
	}
}

.mm-wrapper ul {
	list-style: none;
	display: flex;
	flex-direction: column;
}

.mm-wrapper ul:first-of-kind {
	border-right: 1px solid var(--mm-line);
}

.menu-item {
	padding-block: 0.5rem;
	border-bottom: 1px solid var(--mm-line);
}

.menu-item a {
	font-size: var(--text-m);
	font-weight: 600;
	display: flex;
	width: 100%;
	padding: 1rem;
	border-radius: 0.5rem;
	cursor: pointer;
	transition: background 0.2s ease;
	text-decoration: none;
	color: inherit;
}

.menu-item:hover a {
	background: var(--base-light);
}

.menu-item.active a {
	background: var(--base-light);
	color: inherit;
	font-weight: 600;
}

#children-column {
	padding: 1rem;
}

#children-column h2 {
	font-size: 1.5rem;
	font-weight: 600;
	margin-bottom: 0.5rem;
}

#children-description {
	font-size: 1rem;
	color: #444;
	margin-bottom: 1rem;
	line-height: 1.5;
}

#overview-column {
	padding: 1rem;
	display: flex;
	flex-direction: column;
	background: var(--base-light);
}

#overview-column img {
	width: 100%;
	height: 20rem;
	object-fit: cover;
	object-position: center center;
	border-radius: 0.5rem;
	margin-bottom: 1rem;
	/* order: 1; */
}

#overview-column h3 {
	font-size: 1.25rem;
	font-weight: 700;
	margin: 0.5rem 0;
	order: 2;
}

#overview-column p {
	font-size: 0.9rem;
	line-height: 1.5;
	color: var(--text);
	margin-bottom: 1rem;
	order: 3;
}

#overview-column a {
	display: inline-block;
	margin-top: 0.5rem;
	color: var(--base-light);
	font-weight: 600;
	text-decoration: none;
	order: 4;
}

#overview-column a:hover {
	text-decoration: underline;
}

/* Footer */
footer {
	background: var(--footer);
}

footer h4 {
	font-size: 1.2rem !important;
	text-transform: uppercase;
}

footer a,
footer p {
	color: var(--footer-texts) !important;
	font-size: 0.9rem !important;
	font-weight: 300 !important;
}

footer a.btn {
	color: var(--white) !important;
}

/* General */
section h1,
section h2 {
	color: var(--heading);
	font-size: var(--text-xl);
	text-transform: uppercase;
	width: fit-content;
}

main section {
	display: flex;
	flex-direction: column;
	gap: 0;
}

section .byline h2,
section .byline h3 {
	color: var(--heading);
	font-size: var(--text-s);
	font-weight: 300;
	text-transform: uppercase;
	width: fit-content;
}

section .byline {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	order: -1;
}

section .byline svg {
	display: flex;
	max-width: 1.8rem;
}

.service-hero p,
.service-process p,
.banner-content p {
	font-size: var(--text-m);
}

@media (min-width: 768px) {
	section .byline svg {
		max-width: 2rem;
	}
}

section .byline svg > path {
	fill: var(--heading);
}

/* Service Hero */
.service-hero .cta-box {
	position: relative;
	margin-block: 2rem;
}

.service-hero .cta {
	display: flex;
	justify-content: center;
	width: 100%;
	margin-top: 2rem;
}

.service-hero .cta .icon {
	display: none;
}

@media (min-width: 768px) {
	.service-hero .cta-box {
		margin-block: 4rem;
	}

	.service-hero .cta {
		align-items: center;
		justify-content: space-between;
		position: absolute;
		bottom: 1rem;
		left: 0;
		padding: 0rem 4rem;
		margin: 0;
	}

	.service-hero .cta .icon {
		display: flex;
	}
}

.icon {
	display: flex;
	width: fit-content;
}

.cta .icon svg {
	max-width: 5rem;
	max-height: 5rem;
}

.service-hero .cta a {
	display: inline-flex;
	background: transparent;
	border-radius: 0.5rem;
	border: 1px solid var(--base);
	padding: 1rem;
}

.service-hero .p {
	width: fit-content;
}

/* Service Methods */
.service-methods ul {
	display: grid;
	align-items: stretch;
	gap: 2rem;
	margin-top: 4rem;
}

.service-methods li {
	--card-height: 32em;

	min-height: var(--card-height);
	height: 100%;
	padding: 1rem;
	border-radius: 1.25rem;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.service-methods ul li .wrapper {
	overflow: hidden;
	border-radius: 0.5rem;
}

.service-methods ul li .wrapper .method-card {
	height: 100%;
	min-height: calc(var(--card-height) - 2rem);
	display: flex;
	flex-direction: column;
	gap: 1rem;
	padding: 1rem;
	background: var(--card-header-bg);
	border-radius: 0.5rem;
	font-weight: 400;
	line-height: 150%;
	letter-spacing: 0%;
	transform: translateY(84%);
	transition: transform 1s ease;
}

.service-methods ul li .wrapper .method-card h3 {
	font-size: var(--text-m);
	margin-bottom: 1rem;
}

.service-methods .method:hover .wrapper .method-card {
	transform: translateY(0);
}

@media (min-width: 640px) {
	.service-methods ul {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (min-width: 768px) {
	.service-methods ul {
		grid-template-columns: repeat(4, 1fr);
	}

	.service-methods .method .wrapper .method-card {
		transform: translateY(85%);
	}
}

/* Service Extra */
.service-extra .wrapper {
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	color: var(--white);
	padding: 1rem;
}

.service-extra .wrapper:after {
	content: '';
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(110deg, #07344a 35%, #07344a00);
	backdrop-filter: blur(3px);
	z-index: -1;
	border-radius: 1rem;
}

.service-extra .wrapper svg > path {
	fill: var(--white);
}

.service-extra .wrapper figure {
	position: absolute;
	inset: 0;
	z-index: -1;
}

.service-extra .wrapper figure img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	border-radius: 1rem;
}

.service-extra .wrapper p {
	font-size: var(--text-m);
	padding: 0;
	margin-top: 1rem;
}

.service-extra .wrapper ul {
	display: grid;
	gap: 2rem;
	align-items: stretch;
	margin-top: 2rem;
}

.service-extra .wrapper li {
	background: var(--benefit-bg);
	backdrop-filter: blur(70px);
	border-radius: 0.5rem;
	padding: 1rem;
}

.service-extra .wrapper li h4 {
	font-size: var(--text-m);
	font-weight: 600;
}

.service-extra .wrapper li p {
	font-size: var(--text-s);
}

@media (min-width: 640px) {
	.service-extra .wrapper {
		padding: 2rem;
	}
}

@media (min-width: 768px) {
	.service-extra .wrapper {
		padding: 3rem;
	}

	.service-extra .wrapper p {
		max-width: 65ch;
		font-size: var(--text-m);
	}

	.service-extra .wrapper ul {
		grid-template-columns: repeat(3, 1fr);
	}
}

/* Service Offerings */
.service-offerings__filters-wrapper {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 0.75rem;
}

.service-offerings__filters {
	background: var(--white);
	padding: 0.5rem 1rem;
	border-radius: 0.5rem;
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
	width: 100%;
	margin-top: 2rem;
}

.service-offerings__filters button {
	border: none;
	color: var(--filters);
	background: none;
	font-weight: 600;
	padding: 0.5rem 0.75rem;
	border-radius: 0.375rem;
}

.service-offerings__active-filter h4 {
	font-weight: 600;
}

.service-offerings__filters button:focus,
.service-offerings__filters button:hover,
.service-offerings__filters button:active,
.service-offerings__filters .active {
	background-color: var(--filters-active);
	color: var(--heading);
}

.service-offerings__list {
	display: grid;
	align-items: stretch;
	gap: 1rem;
	margin-top: 4rem;
}

.offering-card article {
	height: 100%;
	padding: 1rem;
	background: var(--white);
	border-radius: 0.5rem;
}

.offering-card article h4 {
	font-size: var(--text-l);
	margin-block: 1rem;
}

@media (min-width: 640px) {
	.service-offerings__filters-wrapper {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}

	.service-offerings__filters {
		width: fit-content;
		gap: 1rem;
	}

	.service-offerings__list {
		flex-direction: row;
		flex-wrap: wrap;
	}
}

@media (min-width: 1024px) {
	.service-offerings__list {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 2rem;
	}
}

/* Section: Benefits */
.service-benefits ul {
	display: grid;
	align-items: stretch;
	gap: 2rem;
	margin-top: 4rem;
}

.service-benefits ul > li {
	display: flex;
}

.service-benefits article {
	height: 100%;
	padding: 1.5rem;
	background: var(--metric-card);
	border-radius: 1rem;
	transition: background 0.3s ease, color 0.3s ease, fill 0.3s ease;
}

.service-benefits article h4 {
	font-size: var(--text-m);
	color: var(--white);
	margin-block: 1rem;
}

.service-benefits article p {
	margin-top: auto;
	color: var(--card-text);
}

.service-benefits article:hover {
	background: var(--base);
}

.service-benefits article .icon {
	position: relative;
	display: inline-block;
	width: 2rem;
	height: 2rem;
}

.service-benefits article:hover .icon svg > path {
	fill: greenyellow;
}

.service-benefits article:hover p {
	color: var(--white);
}

@media (min-width: 640px) {
	.service-benefits ul {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (min-width: 768px) {
	.service-benefits ul {
		grid-template-columns: repeat(4, 1fr);
	}
}

/* Service Process */
.service-process .intro {
	margin-top: 1rem;
}

.service-process__grid {
	display: grid;
	gap: 2rem;
	margin-top: 2rem;
	min-height: 33rem;
}

.service-process__grid > * {
	height: 100%;
}

.service-process__banner {
	padding: 1rem;
	border-radius: 1rem;
	background: var(--footer-heads);
	color: var(--white);
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

.service-process .banner-bg-image {
	position: absolute;
	bottom: -2rem;
	right: 0;
	width: 7rem;
	height: 7rem;
}

.service-process .banner-bg-image img {
	width: 100%;
	height: 100%;
}

.service-process .banner-logo {
	max-width: 5rem;
}

.service-process .banner-tagline {
	margin-top: auto;
}

.service-process .banner-tagline .byline h3 {
	color: var(--white);
}

.service-process .banner-tagline svg > path {
	fill: var(--white);
}

.service-process__list {
	background: var(--white);
	padding: 1rem;
	border-radius: 1rem;
}

.service-process__list h3 {
	font-size: var(--text-l);
	margin: 0;
	margin-bottom: 1rem;
}

.service-process__list ul {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.service-process__list li {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-weight: 500;
}

.service-process__list li svg {
	max-width: 2rem;
}

.service-process__list li svg > path {
	fill: var(--heading);
}

@media (min-width: 640px) {
	.service-process__banner {
		padding: 2rem;
	}

	.service-process .banner-bg-image {
		width: 12rem;
		height: 12rem;
	}
}

@media (min-width: 768px) {
	.service-process__banner {
		padding: 3rem;
	}

	.service-process__grid {
		grid-template-columns: 2fr 1fr;
	}

	.service-process .banner-tagline h4 {
		font-size: 2.125rem;
		max-width: 27ch;
	}

	.service-process .banner-bg-image {
		width: 20rem;
		height: 20rem;
	}

	.service-process__list {
		padding: 2rem;
	}
}

.service-use-cases__list {
	display: grid;
	gap: 2rem;
}

.service-use-cases__list article {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.service-use-cases__list figure {
	order: -1;
}

.service-use-cases__list img {
	border-radius: 1rem;
}

.service-use-cases__list .content {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.service-use-cases__list .content div {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.service-use-cases__list .content h4 {
	font-size: 1.25rem;
}

.service-use-cases__list .content a {
	display: inline-flex;
	align-items: center;
	gap: 1rem;
	text-transform: uppercase;
	color: var(--base);
	font-size: 1.125rem;
	border-bottom: 1px solid var(--heading);
	max-width: fit-content;
}

.service-use-cases__list .client {
	order: -1;
	color: var(--card-text);
}

@media (min-width: 768px) {
	.service-use-cases__list {
		grid-template-columns: repeat(3, 1fr);
	}
	.service-use-cases__list .content div {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		gap: 1rem;
	}
}

.banner-content {
	position: relative;
	padding: 1rem;
	border-radius: 1rem;
	color: var(--white);
	display: grid;
	gap: 2rem;
}

.banner-content:after {
	content: '';
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(100deg, #07344a 35%, #07344a00);
	backdrop-filter: blur(3px);
	z-index: -1;
	border-radius: 1rem;
}

.banner-content figure {
	position: absolute;
	inset: 0;
	z-index: -2;
}

.banner-content figure img {
	width: 100%;
	height: 100%;
	border-radius: 1rem;
	object-fit: cover;
	object-position: center;
}

.banner-content h2 {
	color: var(--white);
}

.banner-content p {
	max-width: 70ch;
}

.banner-content a {
	background: var(--base);
	display: inline-flex;
	align-items: center;
	gap: 1rem;
	padding: 1.5rem 3rem;
	border-radius: 0.5rem;
	width: fit-content;
	font-weight: 600;
	font-size: 1.125rem;
	text-transform: uppercase;
}

.banner-content a svg {
	display: flex;
	transform: rotate(45deg);
}

.banner-content a svg > path {
	fill: var(--white);
}

@media (min-width: 640px) {
	.banner-content {
		padding: 2rem;
	}
}

@media (min-width: 768px) {
	.banner-content {
		min-height: 35dvh;
		padding: 3rem;
		grid-template-columns: 2fr 1fr;
		align-items: center;
		justify-content: space-between;
	}

	.banner-content a {
		padding: 1.5rem 5rem;
		gap: 2rem;
	}
}

/* Single Use Case */
.single-use-case .header {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 0;
	order: -1;
}

.single-use-case .header h2 {
	margin-bottom: 2rem;
}

.single-use-case .header figure {
	display: flex;
	width: 100%;
	max-height: 37.5rem;
}

.single-use-case .header picture {
	display: flex;
	width: 100%;
	height: auto;
}

.single-use-case .header img {
	border-radius: 1rem;
	width: 100%;
	height: auto;
	object-fit: cover;
	object-position: center;
}

.single-use-case .content h1 {
	text-transform: capitalize;
	margin-block: 1rem 2rem;
}

.single-use-case .post-content h4,
p,
li {
	font-size: var(--text-m);
}

.single-use-case .post-content h4,
p,
li:last-child {
	margin-bottom: 1rem;
}

.single-use-case .post-content h4 {
	font-weight: 600;
}

.single-use-case .post-content ul {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
	padding-left: 1rem;
}

.single-use-case .post-content li {
	list-style: disc;
}

/* Related Use Cases */

.related-use-cases h2 {
	margin-bottom: 2rem;
}

.related-grid {
	display: grid;
	align-items: stretch;
	gap: 2rem;
}

.related-grid li {
	display: flex;
}

.related-grid article {
	display: flex;
	flex-direction: column;
}

.related-grid figure {
	order: -1;
}

.related-grid picture {
	display: flex;
}

.related-grid figure img {
	border-radius: 0.5rem;
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	min-height: 20rem;
}

.related-grid .meta {
	display: flex;
	flex-direction: column;
}

.related-grid .client {
	order: -1;
	font-size: var(--text-s);
	color: var(--footer-texts);
	margin-block: 1rem 0.5rem;
}

.related-grid .meta > div {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.related-grid h3 {
	font-size: var(--text-m);
	text-transform: capitalize;
}

.related-grid a {
	font-size: var(--text-s);
	color: var(--base);
	text-transform: uppercase;
	display: inline-flex;
	align-items: center;
	gap: 0.75rem;
	border-bottom: 2px solid var(--heading);
	width: fit-content;
}

.related-grid a svg {
	display: flex;
}

@media (min-width: 640px) {
	.related-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.related-grid figure img {
	}
}

@media (min-width: 768px) {
	.related-grid {
		grid-template-columns: repeat(3, 1fr);
	}

	.related-grid figure img {
		min-height: 32rem;
	}

	.related-grid .meta > div {
		flex-direction: row;
		gap: 1rem;
		align-items: center;
	}
}
