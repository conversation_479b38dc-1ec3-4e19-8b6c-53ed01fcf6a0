document.addEventListener('DOMContentLoaded', () => {
	const filterButtons = document.querySelectorAll('.service-offerings__filters button');
	const cards = document.querySelectorAll('.service-offerings__list .offering-card');
	const activeLabel = document.getElementById('active-filter-label');

	// Function to activate a filter
	function activateFilter(btn) {
		const selected = btn.getAttribute('data-filter');

		// Toggle active class
		filterButtons.forEach(b => b.classList.remove('active'));
		btn.classList.add('active');

		// Update label
		if (activeLabel) {
			activeLabel.textContent = btn.textContent.trim();
		}

		// Show/hide cards
		cards.forEach(card => {
			if (card.getAttribute('data-filter-group') === selected) {
				card.classList.remove('hidden');
			} else {
				card.classList.add('hidden');
			}
		});
	}

	// 👉 Only bind if filters exist
	if (filterButtons.length > 0) {
		// Find initially active button (from PHP)
		const initialBtn = document.querySelector('.service-offerings__filters button.active');

		// Bind click handlers
		filterButtons.forEach(btn => {
			btn.addEventListener('click', () => activateFilter(btn));
		});

		// Activate initial filter on page load
		if (initialBtn) {
			activateFilter(initialBtn);
		}
	} else {
		// No filters case → ensure all cards are visible
		cards.forEach(card => card.classList.remove('hidden'));
	}
});
