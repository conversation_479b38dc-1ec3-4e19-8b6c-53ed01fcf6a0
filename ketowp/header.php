<?php

/**
 * The template for displaying the header
 *
 * Displays all of the head element and everything up until the "keto-content" main tag.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package adun_studio
 * @subpackage KetoWP
 * @since KetoWP 1.0
 */
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo("charset"); ?>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="profile" href="https://gmpg.org/xfn/11" />
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>
    <a class="skip-link screen-reader-text" href="#keto-content">
        /* translators: Hidden accessibility text. */<?php _e(
                                                            "Skip to content",
                                                            "ketowp",
                                                        ); ?>
    </a>

    <header id="keto-header">
        <div class="container flex items-center gap-20 p-4 mx-auto my-12 :lg:px-8">
            <div id="logo_box" class="flex items-center h-[var(--header-height)] min-h-[var(--header-height)] gap-2 justify-start max-w-[12.5rem]">
                <?php
                $site_name = get_bloginfo("name");
                $description = get_bloginfo("description", "display");
                $home = esc_url(home_url("/"));
                ?>

                <button id="hamburger" class="flex flex-col border-0 cursor-pointer size-8 hamburger" aria-label="Toggle navigation menu" aria-expanded="false">
                    <span class="block w-full"></span>
                    <span class="block w-full"></span>
                    <span class="block w-1/2"></span>
                </button>

                <?php
                if (has_custom_logo()): ?>
                    <a class=" logo-wrapper">
                        <?php the_custom_logo(); ?>
                    </a>

                <?php else: ?>
                    <a href="<?php echo $home; ?>" class="no-logo">
                        <h3><?php echo $site_name; ?></h3>
                        <p><?php echo $description; ?></p>
                    </a>
                <?php endif;
                ?>
            </div>

            <nav id="nav_menu" class="flex items-center h-[var(--header-height)] min-h-[var(--header-height)] justify-center max-w-[46rem]">
                <ul class="flex items-center justify-center gap-4">
                    <li><a href="#" class="flex items-center px-8 py-4 bg-transparent hover:bg-slate-300">Link</a></li>
                    <li><a href="#" class="flex items-center px-8 py-4 bg-transparent hover:bg-slate-300">Link</a></li>
                    <li><a href="#" class="flex items-center px-8 py-4 bg-transparent hover:bg-slate-300">Link</a></li>
                    <li><a href="#" class="flex items-center px-8 py-4 bg-transparent hover:bg-slate-300">Link</a></li>
                </ul>
            </nav>

            <div class="ml-auto">
                <button class="flex items-center bg-[var(--base)] text-white rounded px-4 py-2 hover:opacity-90">Let's get started</button>
            </div>

        </div>
    </header>0.75