<?php
add_action('acf/include_fields', function () {
	if (! function_exists('acf_add_local_field_group')) {
		return;
	}

	acf_add_local_field_group(array(
		'key' => 'group_68c2c7f0d158d',
		'title' => 'Homepage Sections',
		'fields' => array(
			array(
				'key' => 'field_68c2c7f182b3c',
				'label' => 'Home Sections',
				'name' => 'home_sections',
				'aria-label' => '',
				'type' => 'flexible_content',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'layouts' => array(
					'layout_68c2c8bc82b41' => array(
						'key' => 'layout_68c2c8bc82b41',
						'name' => 'partners',
						'label' => 'Partners',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2c8de82b43',
								'label' => 'Title',
								'name' => 'title',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2c8e782b44',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2c8ed82b45',
								'label' => 'Description',
								'name' => 'description',
								'aria-label' => '',
								'type' => 'wysiwyg',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'tabs' => 'all',
								'toolbar' => 'full',
								'media_upload' => 1,
								'delay' => 0,
							),
							array(
								'key' => 'field_68c2c8fa82b46',
								'label' => 'Partner List',
								'name' => 'partner_list',
								'aria-label' => '',
								'type' => 'repeater',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'layout' => 'table',
								'min' => 0,
								'max' => 0,
								'collapsed' => '',
								'button_label' => 'Add Row',
								'rows_per_page' => 20,
								'sub_fields' => array(
									array(
										'key' => 'field_68c2c91982b47',
										'label' => 'Partner Logo',
										'name' => 'partner_logo',
										'aria-label' => '',
										'type' => 'image',
										'instructions' => '',
										'required' => 0,
										'conditional_logic' => 0,
										'wrapper' => array(
											'width' => '',
											'class' => '',
											'id' => '',
										),
										'return_format' => 'id',
										'library' => 'all',
										'min_width' => '',
										'min_height' => '',
										'min_size' => '',
										'max_width' => '',
										'max_height' => '',
										'max_size' => '',
										'mime_types' => '',
										'allow_in_bindings' => 0,
										'preview_size' => 'medium',
										'parent_repeater' => 'field_68c2c8fa82b46',
									),
								),
							),
						),
						'min' => '',
						'max' => '',
					),
					'layout_68c2c85d29d68' => array(
						'key' => 'layout_68c2c85d29d68',
						'name' => 'about_us',
						'label' => 'About Us',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2c87082b3d',
								'label' => 'Title',
								'name' => 'title',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2c87a82b3e',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2c88d82b3f',
								'label' => 'Description',
								'name' => 'description',
								'aria-label' => '',
								'type' => 'wysiwyg',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'tabs' => 'all',
								'toolbar' => 'full',
								'media_upload' => 1,
								'delay' => 0,
							),
							array(
								'key' => 'field_68c2c89a82b40',
								'label' => 'Image',
								'name' => 'image',
								'aria-label' => '',
								'type' => 'image',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'return_format' => 'id',
								'library' => 'all',
								'min_width' => '',
								'min_height' => '',
								'min_size' => '',
								'max_width' => '',
								'max_height' => '',
								'max_size' => '',
								'mime_types' => '',
								'allow_in_bindings' => 0,
								'preview_size' => 'medium',
							),
						),
						'min' => '',
						'max' => '',
					),
					'layout_68c2c96627d2e' => array(
						'key' => 'layout_68c2c96627d2e',
						'name' => 'benefits',
						'label' => 'Benefits',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2c98c27d30',
								'label' => 'Title',
								'name' => 'title',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2c99627d31',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2c99c27d32',
								'label' => 'Description',
								'name' => 'description',
								'aria-label' => '',
								'type' => 'wysiwyg',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'tabs' => 'all',
								'toolbar' => 'full',
								'media_upload' => 1,
								'delay' => 0,
							),
							array(
								'key' => 'field_68c2c9c927d33',
								'label' => 'Benefit List',
								'name' => 'benefit_list',
								'aria-label' => '',
								'type' => 'repeater',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'layout' => 'table',
								'min' => 0,
								'max' => 0,
								'collapsed' => '',
								'button_label' => 'Add Row',
								'rows_per_page' => 20,
								'sub_fields' => array(
									array(
										'key' => 'field_68c2c9e427d34',
										'label' => 'Title',
										'name' => 'title',
										'aria-label' => '',
										'type' => 'text',
										'instructions' => '',
										'required' => 0,
										'conditional_logic' => 0,
										'wrapper' => array(
											'width' => '',
											'class' => '',
											'id' => '',
										),
										'default_value' => '',
										'maxlength' => '',
										'allow_in_bindings' => 0,
										'placeholder' => '',
										'prepend' => '',
										'append' => '',
										'parent_repeater' => 'field_68c2c9c927d33',
									),
									array(
										'key' => 'field_68c2c9f627d35',
										'label' => 'Description',
										'name' => 'description',
										'aria-label' => '',
										'type' => 'textarea',
										'instructions' => '',
										'required' => 0,
										'conditional_logic' => 0,
										'wrapper' => array(
											'width' => '',
											'class' => '',
											'id' => '',
										),
										'default_value' => '',
										'maxlength' => '',
										'allow_in_bindings' => 0,
										'rows' => '',
										'placeholder' => '',
										'new_lines' => '',
										'parent_repeater' => 'field_68c2c9c927d33',
									),
									array(
										'key' => 'field_68c2ca0327d36',
										'label' => 'CTA',
										'name' => 'cta',
										'aria-label' => '',
										'type' => 'url',
										'instructions' => '',
										'required' => 0,
										'conditional_logic' => 0,
										'wrapper' => array(
											'width' => '',
											'class' => '',
											'id' => '',
										),
										'default_value' => '',
										'allow_in_bindings' => 0,
										'placeholder' => '',
										'parent_repeater' => 'field_68c2c9c927d33',
									),
								),
							),
						),
						'min' => '',
						'max' => '',
					),
					'layout_68c2ca2ec66f3' => array(
						'key' => 'layout_68c2ca2ec66f3',
						'name' => 'approach',
						'label' => 'Approach',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2ca36c66f5',
								'label' => 'Title',
								'name' => 'title',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2ca3dc66f6',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2ca43c66f7',
								'label' => 'Descrption',
								'name' => 'descrption',
								'aria-label' => '',
								'type' => 'wysiwyg',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'tabs' => 'all',
								'toolbar' => 'full',
								'media_upload' => 1,
								'delay' => 0,
							),
							array(
								'key' => 'field_68c2ca5dc66f8',
								'label' => 'CTA',
								'name' => 'cta',
								'aria-label' => '',
								'type' => 'url',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
							),
						),
						'min' => '',
						'max' => '',
					),
					'layout_68c2ca773677c' => array(
						'key' => 'layout_68c2ca773677c',
						'name' => 'more_benefits',
						'label' => 'More Benefits',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2ca803677e',
								'label' => 'Title',
								'name' => 'more_benefit_list',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2ca993677f',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2ca9f36780',
								'label' => 'Description',
								'name' => 'description',
								'aria-label' => '',
								'type' => 'wysiwyg',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'tabs' => 'all',
								'toolbar' => 'full',
								'media_upload' => 1,
								'delay' => 0,
							),
							array(
								'key' => 'field_68c2caab36781',
								'label' => 'More Benefit List',
								'name' => 'more_benefit_list',
								'aria-label' => '',
								'type' => 'repeater',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'layout' => 'table',
								'min' => 0,
								'max' => 0,
								'collapsed' => '',
								'button_label' => 'Add Row',
								'rows_per_page' => 20,
								'sub_fields' => array(
									array(
										'key' => 'field_68c2cab736782',
										'label' => 'Title',
										'name' => 'title',
										'aria-label' => '',
										'type' => 'text',
										'instructions' => '',
										'required' => 0,
										'conditional_logic' => 0,
										'wrapper' => array(
											'width' => '',
											'class' => '',
											'id' => '',
										),
										'default_value' => '',
										'maxlength' => '',
										'allow_in_bindings' => 0,
										'placeholder' => '',
										'prepend' => '',
										'append' => '',
										'parent_repeater' => 'field_68c2caab36781',
									),
									array(
										'key' => 'field_68c2cac436783',
										'label' => 'Description',
										'name' => 'description',
										'aria-label' => '',
										'type' => 'textarea',
										'instructions' => '',
										'required' => 0,
										'conditional_logic' => 0,
										'wrapper' => array(
											'width' => '',
											'class' => '',
											'id' => '',
										),
										'default_value' => '',
										'maxlength' => '',
										'allow_in_bindings' => 0,
										'rows' => '',
										'placeholder' => '',
										'new_lines' => '',
										'parent_repeater' => 'field_68c2caab36781',
									),
								),
							),
							array(
								'key' => 'field_68c2cad136784',
								'label' => 'CTA',
								'name' => 'cta',
								'aria-label' => '',
								'type' => 'url',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
							),
						),
						'min' => '',
						'max' => '',
					),
					'layout_68c2caeb6f6bb' => array(
						'key' => 'layout_68c2caeb6f6bb',
						'name' => 'use_cases',
						'label' => 'Use Cases',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2caf16f6bd',
								'label' => 'Title',
								'name' => 'title',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2cafc6f6be',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
						),
						'min' => '',
						'max' => '',
					),
					'layout_68c2cb0e6f6bf' => array(
						'key' => 'layout_68c2cb0e6f6bf',
						'name' => 'banner',
						'label' => 'Banner',
						'display' => 'block',
						'sub_fields' => array(
							array(
								'key' => 'field_68c2cb146f6c1',
								'label' => 'Title',
								'name' => 'title',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2cb1c6f6c2',
								'label' => 'Byline',
								'name' => 'byline',
								'aria-label' => '',
								'type' => 'text',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
								'prepend' => '',
								'append' => '',
							),
							array(
								'key' => 'field_68c2cb256f6c3',
								'label' => 'Description',
								'name' => 'description',
								'aria-label' => '',
								'type' => 'textarea',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'maxlength' => '',
								'allow_in_bindings' => 0,
								'rows' => '',
								'placeholder' => '',
								'new_lines' => '',
							),
							array(
								'key' => 'field_68c2cb346f6c4',
								'label' => 'CTA',
								'name' => 'cta',
								'aria-label' => '',
								'type' => 'url',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'default_value' => '',
								'allow_in_bindings' => 0,
								'placeholder' => '',
							),
							array(
								'key' => 'field_68c2cb3e6f6c5',
								'label' => 'Image',
								'name' => 'image',
								'aria-label' => '',
								'type' => 'image',
								'instructions' => '',
								'required' => 0,
								'conditional_logic' => 0,
								'wrapper' => array(
									'width' => '',
									'class' => '',
									'id' => '',
								),
								'return_format' => 'id',
								'library' => 'all',
								'min_width' => '',
								'min_height' => '',
								'min_size' => '',
								'max_width' => '',
								'max_height' => '',
								'max_size' => '',
								'mime_types' => '',
								'allow_in_bindings' => 0,
								'preview_size' => 'medium',
							),
						),
						'min' => '',
						'max' => '',
					),
				),
				'min' => '',
				'max' => '',
				'button_label' => 'Add Row',
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'page',
					'operator' => '==',
					'value' => '2881',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 0,
	));
});
