<?php

/**
 * Renderer: frontpage
 */
if (!function_exists('integr8ai_render_frontpage')) {
	function integr8ai_render_frontpage()
	{
		global $post;

		// Early return if no post
		if (!isset($post) || !$post instanceof WP_Post) {
			return '';
		}

		$post_id = absint($post->ID);
		$title = get_the_title($post);
		$content = wp_kses_post(apply_filters('the_content', $post->post_content));

		// Validate required data
		if (empty($title)) {
			return '';
		}

		ob_start();
?>
		<section class="container hero" aria-labelledby="frontpage-title">
			<div class="wrapper">
				<h1 id="frontpage-title"><?php echo esc_html($title); ?></h1>

				<div class="post-content">
					<?php echo $content; ?>

					<?php if (has_post_thumbnail($post_id)):
						$thumb_id = get_post_thumbnail_id($post_id);
						$img_url = wp_get_attachment_image_url($thumb_id, 'full');
						$img_alt = get_post_meta($thumb_id, '_wp_attachment_image_alt', true) ?: $title;

						if ($img_url): ?>
							<figure class="featured-image">
								<picture>
									<img src="<?php echo esc_url($img_url); ?>" alt="<?php echo esc_attr($img_alt); ?>" loading="lazy" width="800" height="600" />
								</picture>
							</figure>
						<?php endif; ?>
					<?php endif; ?>
				</div>
			</div>
		</section>

		<?php
		// Render flexible content sections
		if (have_rows('home_sections', $post_id)):
			while (have_rows('home_sections', $post_id)): the_row();
				$layout = get_row_layout();

				switch ($layout):
					case 'partners':
						$title = get_sub_field('title');
						$byline = get_sub_field('byline');
						$description = get_sub_field('description');
		?>
						<section class="container partners" aria-labelledby="partners-title">
							<?php if (!empty($title)): ?>
								<h2 id="partners-title"><?php echo esc_html($title); ?></h2>
							<?php endif; ?>

							<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
								<?php echo integr8ai_render_byline($byline); ?>
							<?php endif; ?>

							<?php if (!empty($description)): ?>
								<div><?php echo wp_kses_post($description); ?></div>
							<?php endif; ?>

							<?php if (have_rows('partner_list')): ?>
								<ul>
									<?php while (have_rows('partner_list')): the_row();
										$logo_id = get_sub_field('partner_logo');
										if (!empty($logo_id)):
											$logo_url = wp_get_attachment_image_url($logo_id, 'full');
											$logo_alt = get_post_meta($logo_id, '_wp_attachment_image_alt', true) ?: 'Partner logo';
									?>
											<li>
												<figure>
													<picture>
														<img src="<?php echo esc_url($logo_url); ?>"
															alt="<?php echo esc_attr($logo_alt); ?>"
															loading="lazy" />
													</picture>
												</figure>
											</li>
									<?php endif;
									endwhile; ?>
								</ul>
							<?php endif; ?>
						</section>
					<?php
						break;

					case 'about_us':
						$title = get_sub_field('title');
						$byline = get_sub_field('byline');
						$description = get_sub_field('description');
						$image_id = get_sub_field('image');
						$cta = get_sub_field('cta');
					?>
						<section class="container about-us" aria-labelledby="about-us-title">
							<div class="wrapper">
								<div class="left">
									<?php if (!empty($title)): ?>
										<h2 id="about-us-title"><?php echo esc_html($title); ?></h2>
									<?php endif; ?>

									<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
										<?php echo integr8ai_render_byline($byline); ?>
									<?php endif; ?>

									<?php if (!empty($description)): ?>
										<div><?php echo wp_kses_post($description); ?></div>
									<?php endif; ?>

									<?php if (!empty($cta) && is_array($cta) && !empty($cta['url'])): ?>
										<a href="<?php echo esc_url($cta['url']); ?>" rel="noopener">
											<span><?php echo !empty($cta['title']) ? esc_html($cta['title']) : 'Learn more'; ?></span>
											<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
												<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
											<?php endif; ?>
										</a>
									<?php endif; ?>
								</div>

								<div class="right">
									<?php if (!empty($image_id)):
										$image_url = wp_get_attachment_image_url($image_id, 'full');
										$image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true) ?: 'About us';
									?>
										<figure>
											<picture>
												<img src="<?php echo esc_url($image_url); ?>"
													alt="<?php echo esc_attr($image_alt); ?>"
													loading="lazy">
											</picture>
										</figure>
										<div class="experience">
											<span class="years">3+</span>
											<span class="years_text">Years of experience</span>
										</div>
									<?php endif; ?>
								</div>
							</div>
						</section>
					<?php
						break;

					case 'benefits':
						$title = get_sub_field('title');
						$byline = get_sub_field('byline');
						$description = get_sub_field('description');
					?>
						<section class="container key-value-proposition" aria-labelledby="benefits-title">
							<?php if (!empty($title)): ?>
								<h2 id="benefits-title"><?php echo esc_html($title); ?></h2>
							<?php endif; ?>

							<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
								<?php echo integr8ai_render_byline($byline); ?>
							<?php endif; ?>

							<?php if (!empty($description)): ?>
								<div><?php echo wp_kses_post($description); ?></div>
							<?php endif; ?>

							<?php if (have_rows('benefit_list')): ?>
								<ul>
									<?php while (have_rows('benefit_list')): the_row();
										$benefit_title = get_sub_field('title');
										$benefit_description = get_sub_field('description');
										$benefit_bg = get_sub_field('image');
										$benefit_cta = get_sub_field('cta');

										if (!empty($benefit_title) || !empty($benefit_description)): ?>
											<li class="method" style="background-image: url('<?php echo !empty($benefit_bg) ? esc_url($benefit_bg['url']) : ''; ?>');">
												<div class="wrapper">
													<article class="method-card">
														<?php if (!empty($benefit_title)): ?>
															<h3><?php echo esc_html($benefit_title); ?></h3>
														<?php endif; ?>
														<?php if (!empty($benefit_description)): ?>
															<p><?php echo wp_kses_post($benefit_description); ?></p>
														<?php endif; ?>
														<?php if (!empty($benefit_cta) && is_array($benefit_cta) && !empty($benefit_cta['url'])): ?>
															<a href="<?php echo esc_url($benefit_cta['url']); ?>" rel="noopener">
																<span><?php echo !empty($benefit_cta['title']) ? esc_html($benefit_cta['title']) : 'Learn more'; ?></span>
																<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
																	<span><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
																<?php endif; ?>
															</a>
														<?php endif; ?>
													</article>
												</div>
											</li>
									<?php endif;
									endwhile; ?>
								</ul>
							<?php endif; ?>
						</section>
					<?php
						break;

					case 'approach':
						$title = get_sub_field('title');
						$byline = get_sub_field('byline');
						$description = get_sub_field('description');
						$cta = get_sub_field('cta');
					?>
						<section class="container approach" aria-labelledby="approach-title">
							<?php if (!empty($title)): ?>
								<h2 id="approach-title"><?php echo esc_html($title); ?></h2>
							<?php endif; ?>

							<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
								<?php echo integr8ai_render_byline($byline); ?>
							<?php endif; ?>

							<?php if (!empty($description)): ?>
								<div><?php echo wp_kses_post($description); ?></div>
							<?php endif; ?>

							<?php if (!empty($cta) && is_array($cta) && !empty($cta['url'])): ?>
								<a href="<?php echo esc_url($cta['url']); ?>" rel="noopener">
									<span><?php echo !empty($cta['title']) ? esc_html($cta['title']) : 'Learn more'; ?></span>
									<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
										<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
									<?php endif; ?>
								</a>
							<?php endif; ?>
						</section>
					<?php
						break;

					case 'more_benefits':
						$title = get_sub_field('title');
						$description = get_sub_field('description');
						$cta = get_sub_field('cta');
					?>
						<section class="container more-benefits" aria-labelledby="more-benefits-title">
							<div class="wrapper">
								<?php if (!empty($title)): ?>
									<h2 id="more-benefits-title"><?php echo esc_html($title); ?></h2>
								<?php endif; ?>

								<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
									<?php echo integr8ai_render_byline($byline); ?>
								<?php endif; ?>

								<?php if (!empty($description)): ?>
									<div class="post-content"><?php echo wp_kses_post($description); ?></div>
								<?php endif; ?>

								<?php if (have_rows('more_benefit_list')): ?>
									<ul>
										<?php while (have_rows('more_benefit_list')): the_row();
											$benefit_title = get_sub_field('title');
											$benefit_description = get_sub_field('description');

											if (!empty($benefit_title) || !empty($benefit_description)): ?>
												<li>
													<article>
														<?php if (!empty($benefit_title)): ?>
															<h3><?php echo esc_html($benefit_title); ?></h3>
														<?php endif; ?>
														<?php if (!empty($benefit_description)): ?>
															<p><?php echo wp_kses_post($benefit_description); ?></p>
														<?php endif; ?>
													</article>
												</li>
										<?php endif;
										endwhile; ?>
									</ul>
								<?php endif; ?>

								<?php if (!empty($cta) && is_array($cta) && !empty($cta['url'])): ?>
									<a href="<?php echo esc_url($cta['url']); ?>" rel="noopener">
										<span><?php echo !empty($cta['title']) ? esc_html($cta['title']) : 'Explore more'; ?></span>
										<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
											<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
										<?php endif; ?>
									</a>
								<?php endif; ?>
							</div>
						</section>
					<?php
						break;

					case 'use_cases':
						$title = get_sub_field('title');
						$byline = get_sub_field('byline');
					?>
						<?php if (!empty($title) || !empty($byline)): ?>
							<section class="container use-cases" aria-labelledby="use-cases-title">
								<?php if (!empty($title)): ?>
									<h2 id="use-cases-title"><?php echo esc_html($title); ?></h2>
								<?php endif; ?>

								<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
									<?php echo integr8ai_render_byline($byline); ?>
								<?php endif; ?>

								<?php if (shortcode_exists('related_use_cases')): ?>
									<?php echo do_shortcode('[related_use_cases]'); ?>
								<?php endif; ?>
							</section>
						<?php endif; ?>
					<?php
						break;

					case 'banner':
						$title = get_sub_field('title');
						$byline = get_sub_field('byline');
						$description = get_sub_field('description');
						$cta = get_sub_field('cta');
						$image_id = get_sub_field('image');
					?>
						<section class="container home-banner" aria-labelledby="banner-title">
							<div class="wrapper">
								<div>
									<?php if (!empty($title)): ?>
										<h2 id="banner-title"><?php echo esc_html($title); ?></h2>
									<?php endif; ?>

									<?php if (!empty($byline) && function_exists('integr8ai_render_byline')): ?>
										<?php echo integr8ai_render_byline($byline); ?>
									<?php endif; ?>

									<?php if (!empty($description)): ?>
										<p><?php echo wp_kses_post($description); ?></p>
									<?php endif; ?>

									<?php if (!empty($cta) && is_array($cta) && !empty($cta['url'])): ?>
										<a href="<?php echo esc_url($cta['url']); ?>" rel="noopener">
											<span><?php echo !empty($cta['title']) ? esc_html($cta['title']) : 'Discover more'; ?></span>
											<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
												<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
											<?php endif; ?>
										</a>
									<?php endif; ?>
								</div>

								<?php if (!empty($image_id)):
									$image_url = wp_get_attachment_image_url($image_id, 'large');
									$image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true) ?: 'Banner image';
								?>
									<figure>
										<picture>
											<img src="<?php echo esc_url($image_url); ?>"
												alt="<?php echo esc_attr($image_alt); ?>"
												loading="lazy">
										</picture>
									</figure>
								<?php endif; ?>
							</div>
						</section>
		<?php
						break;

				endswitch;
			endwhile;
		endif;
		?>
<?php
		return ob_get_clean();
	}
}

// Register shortcode with validation
add_shortcode('frontpage', function ($atts = []) {
	// Validate shortcode is being called in appropriate context for pages
	if (!is_admin() && !wp_doing_ajax()) {
		return integr8ai_render_frontpage();
	}
	return '';
});
