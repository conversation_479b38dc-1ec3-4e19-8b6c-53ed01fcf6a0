<?php

/**
 * Renderer: frontpage
 */
if (!function_exists('integr8ai_render_frontpage')) {
	function integr8ai_render_frontpage()
	{
		global $post;
		if (!isset($post)) {
			return '';
		}

		$post_id = $post->ID;
		$title = get_the_title($post);
		$content = wp_kses_post(apply_filters('the_content', $post->post_content));

		ob_start();
?>
		<section class="container hero" aria-labelledby="frontpage-title">
			<div class="wrapper">
				<h1 id="frontpage-title"><?php echo esc_html($title); ?></h1>

				<div class="post-content">
					<?php echo $content; ?>

					<?php if (has_post_thumbnail($post_id)):
						$thumb_id = get_post_thumbnail_id($post_id);
						$img_url = wp_get_attachment_image_url($thumb_id, 'full');
						$img_alt = get_post_meta($thumb_id, '_wp_attachment_image_alt', true) ?: $title;
					?>
						<figure class="featured-image">
							<picture>
								<img src="<?php echo esc_url($img_url); ?>" alt="<?php echo esc_attr($img_alt); ?>" loading="lazy" />
							</picture>
						</figure>
					<?php endif; ?>
				</div>
			</div>
		</section>

		<section class="container partners" aria-labelledby="trusted-partners">
			<h2 id="trusted-partners">Trusted Partners</h2>
			<?php if (have_rows('partners', $post_id)): ?>
				<ul>
					<?php while (have_rows('partners', $post_id)): the_row();
						$image = get_sub_field('partner_logo');
						if ($image):
							$url = $image['url'];
							$alt = $image['alt'] ?: $image['title'];
					?>
							<li>
								<figure>
									<picture>
										<img src="<?php echo esc_url($url); ?>" alt="<?php echo esc_attr($alt); ?>" loading="lazy" />
									</picture>
								</figure>
							</li>
					<?php endif;
					endwhile; ?>
				</ul>
			<?php endif; ?>
		</section>

		<section class="container about-us" aria-labelledby="about-us">
			<div class="wrapper">
				<div class="left">
					<h2 id="about-us"><?php echo esc_html(get_field('about_us')) ?></h2>
					<?php echo integr8ai_render_byline('About us') ?>
					<div><?php echo esc_html(get_field('about_us_description')) ?></div>
					<a href="<?php echo esc_url(get_field('about_us_cta_link')) ?>">
						<span>Find out more</span>
						<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option') ?></span>
					</a>
				</div>

				<div class="right">
					<?php
					$about_image = get_sub_field('about_us_image');
					if ($about_image):
						$about_url = $about_image['url'];
						$about_alt = $about_image['alt'] ?: $about_image['title'];
					?>
						<figure>
							<picture>
								<img src="<?php echo $about_url ?>" alt="<?php echo $about_alt ?>">
							</picture>
						</figure>
						<div class="experience">
							<span class="years">3+</span>
							<span class="years_text">Years of experience</span>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</section>

		<section class="container key-value-proposition" aria-labelledby="key-value-proposition">
			<h2 id="key-value-proposition"><?php echo esc_html(get_field('proposition_title')) ?></h2>
			<?php echo integr8ai_render_byline('Our benefits') ?>
			<div><?php echo esc_html(get_field('proposition_description')) ?></div>

			<?php if (have_rows('benefits', $post_id)): ?>
				<ul>
					<?php while (have_rows('benefits', $post_id)): the_row();
						$bg_id = get_sub_field('bg_image');
						$bg_url = $bg_id ? wp_get_attachment_image_url($bg_id, 'large') : '';
						$title = get_sub_field('title');
						$description = get_sub_field('description');
						$cta = get_sub_field('cta');
					?>
						<li class="method" <?php echo $bg_url ? ' style="background-image:url(' . esc_url($bg_url) . ')"' : ''; ?>>
							<div class="wrapper">
								<article>
									<?php if ($title): ?>
										<h3><?php echo esc_html($title); ?></h3>
									<?php endif; ?>
									<?php if ($description): ?>
										<p><?php echo esc_html($description); ?></p>
									<?php endif; ?>
									<?php if ($cta); ?>
									<a href="<?php echo esc_url($cta['url']) ?>">
										<span><?php echo esc_html($cta['title']) ?></span>
										<span><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option') ?></span>
									</a>
								</article>
							</div>
						</li>
					<?php endwhile; ?>
				</ul>
			<?php endif; ?>
		</section>

		<section class="container approach" aria-labelledby="approach-title">
			<h2 id="approach-title"><?php echo esc_html(get_field('approach_title')) ?></h2>
			<?php echo integr8ai_render_byline('Artificial Intelligence') ?>
			<div><?php echo esc_html(get_field('approach_description')) ?></div>
			<a href="<?php echo esc_url(get_field('approach_cta_link')) ?>">
				<span>Learn more</span>
				<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option') ?></span>
			</a>
		</section>

		<section class="container more-benefits" aria-labelledby="more-benefits-title">
			<div class="wrapper">
				<h2 id="more-benefits-title"><?php echo esc_html(get_field('more_benefits_title')) ?></h2>
				<?php echo integr8ai_render_byline('Benefits to your business') ?>
				<div class="post-content"><?php echo esc_html(get_field('more_benefits_description')) ?></div>

				<?php if (have_rows('benefits', $post_id)): ?>
					<ul>
						<?php while (have_rows('more_benefits', $post_id)): the_row();
							$title = get_sub_field('title');
							$description = get_sub_field('description');
						?>
							<li>
								<article>
									<h3><?php echo esc_html($title) ?></h3>
									<p><?php echo esc_html($description) ?></p>
								</article>
							</li>
						<?php endwhile ?>
					</ul>
				<?php endif ?>

				<a href="<?php echo esc_url(get_field('more-benefits_cta_link')); ?>">
					<span>Explore more</span>
					<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option') ?></span>
				</a>
			</div>
		</section>

		<?php echo do_shortcode('related_use_cases') ?>

		<section class="container home-banner" aria-labelledby="home-banner-title">
			<div class="wrapper">

				<h2 id="home-banner-title"><?php echo esc_html(get_field('home_banner_title')) ?></h2>
				<?php echo integr8ai_render_byline('Transforming Work, Empowering People') ?>
				<p><?php echo esc_html(get_field('home_banner_description')) ?></p>
				<a href="<?php echo esc_url(get_field('home_banner_cta_link')); ?>">
					<span>Discover more</span>
					<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option') ?></span>
				</a>
				<?php
				$bg_id = get_field('banner_image');
				$bg_url = $bg_id ? wp_get_attachment_image_url($bg_id, 'large') : '';
				?>
				<figure>
					<picture>
						<img src="<?php echo $bg_url ?>" alt="<?php echo $bg_alt ?>">
					</picture>
				</figure>
			</div>
		</section>
<?php
		return ob_get_clean();
	}
}

add_shortcode('frontpage', function ($atts = []) {
	return integr8ai_render_frontpage();
});
