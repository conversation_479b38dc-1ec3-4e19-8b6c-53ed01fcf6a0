<?php

/**
 * Renderer: frontpage
 */
if (!function_exists('integr8ai_render_frontpage')) {
	function integr8ai_render_frontpage()
	{
		global $post;

		// Early return if no post
		if (!isset($post) || !$post instanceof WP_Post) {
			return '';
		}

		$post_id = absint($post->ID);
		$title = get_the_title($post);
		$content = wp_kses_post(apply_filters('the_content', $post->post_content));

		// Validate required data
		if (empty($title)) {
			return '';
		}

		ob_start();
?>
		<section class="container hero" aria-labelledby="frontpage-title">
			<div class="wrapper">
				<h1 id="frontpage-title"><?php echo esc_html($title); ?></h1>

				<div class="post-content">
					<?php echo $content; ?>

					<?php if (has_post_thumbnail($post_id)):
						$thumb_id = get_post_thumbnail_id($post_id);
						$img_url = wp_get_attachment_image_url($thumb_id, 'full');
						$img_alt = get_post_meta($thumb_id, '_wp_attachment_image_alt', true) ?: $title;

						if ($img_url): ?>
							<figure class="featured-image">
								<picture>
									<img src="<?php echo esc_url($img_url); ?>" alt="<?php echo esc_attr($img_alt); ?>" loading="lazy" width="800" height="600" />
								</picture>
							</figure>
						<?php endif; ?>
					<?php endif; ?>
				</div>
			</div>
		</section>

		<section class="container partners" aria-labelledby="trusted-partners">
			<h2 id="trusted-partners">Trusted Partners</h2>
			<?php if (have_rows('partners', $post_id)): ?>
				<ul>
					<?php while (have_rows('partners', $post_id)): the_row();
						$image = get_sub_field('partner_logo');
						if (!empty($image) && is_array($image) && !empty($image['url'])): ?>
							<li>
								<figure>
									<picture>
										<img src="<?php echo esc_url($image['url']); ?>"
											alt="<?php echo esc_attr(!empty($image['alt']) ? $image['alt'] : (!empty($image['title']) ? $image['title'] : 'Partner logo')); ?>"
											loading="lazy"
											width="200"
											height="100" />
									</picture>
								</figure>
							</li>
					<?php endif;
					endwhile; ?>
				</ul>
			<?php endif; ?>
		</section>

		<?php
		$about_title = get_field('about_us', $post_id);
		$about_description = get_field('about_us_description', $post_id);
		$about_cta_link = get_field('about_us_cta_link', $post_id);
		$about_image = get_field('about_us_image', $post_id);

		if (!empty($about_title) || !empty($about_description)): ?>
			<section class="container about-us" aria-labelledby="about-us">
				<div class="wrapper">
					<div class="left">
						<?php if (!empty($about_title)): ?>
							<h2 id="about-us"><?php echo esc_html($about_title); ?></h2>
						<?php endif; ?>

						<?php if (function_exists('integr8ai_render_byline')): ?>
							<?php echo integr8ai_render_byline('About us'); ?>
						<?php endif; ?>

						<?php if (!empty($about_description)): ?>
							<div><?php echo wp_kses_post($about_description); ?></div>
						<?php endif; ?>

						<?php if (!empty($about_cta_link)): ?>
							<a href="<?php echo esc_url($about_cta_link); ?>" rel="noopener">
								<span>Find out more</span>
								<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
									<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
								<?php endif; ?>
							</a>
						<?php endif; ?>
					</div>

					<div class="right">
						<?php if (!empty($about_image) && is_array($about_image) && !empty($about_image['url'])): ?>
							<figure>
								<picture>
									<img src="<?php echo esc_url($about_image['url']); ?>"
										alt="<?php echo esc_attr(!empty($about_image['alt']) ? $about_image['alt'] : (!empty($about_image['title']) ? $about_image['title'] : 'About us')); ?>"
										loading="lazy"
										width="600"
										height="400">
								</picture>
							</figure>
							<div class="experience">
								<span class="years">3+</span>
								<span class="years_text">Years of experience</span>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</section>
		<?php endif; ?>

		<?php
		$proposition_title = get_field('proposition_title', $post_id);
		$proposition_description = get_field('proposition_description', $post_id);

		if (!empty($proposition_title) || !empty($proposition_description) || have_rows('benefits', $post_id)): ?>
			<section class="container key-value-proposition" aria-labelledby="key-value-proposition">
				<?php if (!empty($proposition_title)): ?>
					<h2 id="key-value-proposition"><?php echo esc_html($proposition_title); ?></h2>
				<?php endif; ?>

				<?php if (function_exists('integr8ai_render_byline')): ?>
					<?php echo integr8ai_render_byline('Our benefits'); ?>
				<?php endif; ?>

				<?php if (!empty($proposition_description)): ?>
					<div><?php echo wp_kses_post($proposition_description); ?></div>
				<?php endif; ?>

				<?php if (have_rows('benefits', $post_id)): ?>
					<ul>
						<?php while (have_rows('benefits', $post_id)): the_row();
							$bg_id = get_sub_field('bg_image');
							$bg_url = !empty($bg_id) ? wp_get_attachment_image_url($bg_id, 'large') : '';
							$title = get_sub_field('title');
							$description = get_sub_field('description');
							$cta = get_sub_field('cta');

							if (!empty($title) || !empty($description)): ?>
								<li class="method" <?php echo !empty($bg_url) ? ' style="background-image:url(' . esc_url($bg_url) . ')"' : ''; ?>>
									<div class="wrapper">
										<article>
											<?php if (!empty($title)): ?>
												<h3><?php echo esc_html($title); ?></h3>
											<?php endif; ?>
											<?php if (!empty($description)): ?>
												<p><?php echo wp_kses_post($description); ?></p>
											<?php endif; ?>
											<?php if (!empty($cta) && is_array($cta) && !empty($cta['url']) && !empty($cta['title'])): ?>
												<a href="<?php echo esc_url($cta['url']); ?>" rel="noopener">
													<span><?php echo esc_html($cta['title']); ?></span>
													<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
														<span><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
													<?php endif; ?>
												</a>
											<?php endif; ?>
										</article>
									</div>
								</li>
						<?php endif;
						endwhile; ?>
					</ul>
				<?php endif; ?>
			</section>
		<?php endif; ?>

		<?php
		$approach_title = get_field('approach_title', $post_id);
		$approach_description = get_field('approach_description', $post_id);
		$approach_cta_link = get_field('approach_cta_link', $post_id);

		if (!empty($approach_title) || !empty($approach_description)): ?>
			<section class="container approach" aria-labelledby="approach-title">
				<?php if (!empty($approach_title)): ?>
					<h2 id="approach-title"><?php echo esc_html($approach_title); ?></h2>
				<?php endif; ?>

				<?php if (function_exists('integr8ai_render_byline')): ?>
					<?php echo integr8ai_render_byline('Artificial Intelligence'); ?>
				<?php endif; ?>

				<?php if (!empty($approach_description)): ?>
					<div><?php echo wp_kses_post($approach_description); ?></div>
				<?php endif; ?>

				<?php if (!empty($approach_cta_link)): ?>
					<a href="<?php echo esc_url($approach_cta_link); ?>" rel="noopener">
						<span>Learn more</span>
						<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
							<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
						<?php endif; ?>
					</a>
				<?php endif; ?>
			</section>
		<?php endif; ?>

		<?php
		$more_benefits_title = get_field('more_benefits_title', $post_id);
		$more_benefits_description = get_field('more_benefits_description', $post_id);
		$more_benefits_cta_link = get_field('more_benefits_cta_link', $post_id); // Fixed typo: was 'more-benefits_cta_link'

		if (!empty($more_benefits_title) || !empty($more_benefits_description) || have_rows('more_benefits', $post_id)): ?>
			<section class="container more-benefits" aria-labelledby="more-benefits-title">
				<div class="wrapper">
					<?php if (!empty($more_benefits_title)): ?>
						<h2 id="more-benefits-title"><?php echo esc_html($more_benefits_title); ?></h2>
					<?php endif; ?>

					<?php if (function_exists('integr8ai_render_byline')): ?>
						<?php echo integr8ai_render_byline('Benefits to your business'); ?>
					<?php endif; ?>

					<?php if (!empty($more_benefits_description)): ?>
						<div class="post-content"><?php echo wp_kses_post($more_benefits_description); ?></div>
					<?php endif; ?>

					<?php if (have_rows('more_benefits', $post_id)): ?>
						<ul>
							<?php while (have_rows('more_benefits', $post_id)): the_row();
								$title = get_sub_field('title');
								$description = get_sub_field('description');

								if (!empty($title) || !empty($description)): ?>
									<li>
										<article>
											<?php if (!empty($title)): ?>
												<h3><?php echo esc_html($title); ?></h3>
											<?php endif; ?>
											<?php if (!empty($description)): ?>
												<p><?php echo wp_kses_post($description); ?></p>
											<?php endif; ?>
										</article>
									</li>
							<?php endif;
							endwhile; ?>
						</ul>
					<?php endif; ?>

					<?php if (!empty($more_benefits_cta_link)): ?>
						<a href="<?php echo esc_url($more_benefits_cta_link); ?>" rel="noopener">
							<span>Explore more</span>
							<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
								<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
							<?php endif; ?>
						</a>
					<?php endif; ?>
				</div>
			</section>
		<?php endif; ?>

		<?php
		// Only render if shortcode function exists
		if (shortcode_exists('related_use_cases')): ?>
			<?php echo do_shortcode('[related_use_cases]'); ?>
		<?php endif; ?>

		<?php
		$home_banner_title = get_field('home_banner_title', $post_id);
		$home_banner_description = get_field('home_banner_description', $post_id);
		$home_banner_cta_link = get_field('home_banner_cta_link', $post_id);
		$banner_image = get_field('banner_image', $post_id);

		if (!empty($home_banner_title) || !empty($home_banner_description)): ?>
			<section class="container home-banner" aria-labelledby="home-banner-title">
				<div class="wrapper">
					<div>
						<?php if (!empty($home_banner_title)): ?>
							<h2 id="home-banner-title"><?php echo esc_html($home_banner_title); ?></h2>
						<?php endif; ?>

						<?php if (function_exists('integr8ai_render_byline')): ?>
							<?php echo integr8ai_render_byline('Transforming Work, Empowering People'); ?>
						<?php endif; ?>

						<?php if (!empty($home_banner_description)): ?>
							<p><?php echo wp_kses_post($home_banner_description); ?></p>
						<?php endif; ?>

						<?php if (!empty($home_banner_cta_link)): ?>
							<a href="<?php echo esc_url($home_banner_cta_link); ?>" rel="noopener">
								<span>Discover more</span>
								<?php if (function_exists('integr8ai_get_svg_from_acf')): ?>
									<span class="icon"><?php echo integr8ai_get_svg_from_acf('arrow_icon', 'option'); ?></span>
								<?php endif; ?>
							</a>
						<?php endif; ?>
					</div>

					<?php if (!empty($banner_image) && is_array($banner_image) && !empty($banner_image['url'])):
						$bg_alt = !empty($banner_image['alt']) ? $banner_image['alt'] : (!empty($banner_image['title']) ? $banner_image['title'] : 'Banner image'); ?>
						<figure>
							<picture>
								<img src="<?php echo esc_url($banner_image['url']); ?>"
									alt="<?php echo esc_attr($bg_alt); ?>"
									loading="lazy">
							</picture>
						</figure>
					<?php endif; ?>
				</div>
			</section>
		<?php endif; ?>
<?php
		return ob_get_clean();
	}
}

// Register shortcode with validation
add_shortcode('frontpage', function ($atts = []) {
	// Validate shortcode is being called in appropriate context for pages
	if (!is_admin() && !wp_doing_ajax()) {
		return integr8ai_render_frontpage();
	}
	return '';
});
