<?php

/**
 * Renderer: related use cases
 */
if (!function_exists('integr8ai_render_related_use_cases')) {
	function integr8ai_render_related_use_cases($atts = [])
	{
		global $post;
		if (!isset($post) || 'use_case' !== get_post_type($post)) {
			return '';
		}

		$post_id = $post->ID;

		// Query 3 related posts (excluding current)
		$args = [
			'post_type'      => 'use_case',
			'posts_per_page' => 3,
			'post__not_in'   => [$post_id],
			'order'          => 'ASC',
			'orderby'        => 'menu_order',
		];
		$related = new WP_Query($args);

		if (!$related->have_posts()) {
			return '';
		}

		ob_start(); ?>
		<section class="container related-use-cases" aria-labelledby="related-use-cases-title">
			<h2 id="related-use-cases-title">Other Use Cases</h2>
			<?php echo integr8ai_render_byline('Our use cases'); ?>

			<ul class="related-grid">
				<?php while ($related->have_posts()): $related->the_post();
					$client   = get_field('client', get_the_ID());
					$card_title = get_field('card_title', get_the_ID());
					$link     = get_permalink();
					$thumb_id = get_post_thumbnail_id();
					$img_url  = $thumb_id ? wp_get_attachment_image_url($thumb_id, 'full') : '';
					$img_alt  = $thumb_id ? (get_post_meta($thumb_id, '_wp_attachment_image_alt', true) ?: $card_title) : '';
					$arrow_icon = function_exists('integr8ai_get_svg_from_acf')
						? integr8ai_get_svg_from_acf('arrow_icon', 'option')
						: '';
				?>
					<li>
						<article class="related-item">
							<div class="meta">
								<div>
									<h3 class="card-title"><?php echo esc_html($card_title); ?></h3>

									<a href="<?php echo esc_url($link); ?>">
										<span>Learn more</span>
										<?php if ($arrow_icon): ?>
											<span class="icon"><?php echo $arrow_icon; ?></span>
										<?php endif; ?>
									</a>
								</div>

								<?php if ($client): ?>
									<h4 class="client"><?php echo esc_html($client); ?></h4>
								<?php endif; ?>
							</div>

							<?php if ($img_url): ?>
								<figure class="featured-image">
									<picture>
										<img src="<?php echo esc_url($img_url); ?>" alt="<?php echo esc_attr($img_alt); ?>" loading="lazy" />
									</picture>
								</figure>
							<?php endif; ?>
						</article>
					</li>
				<?php endwhile;
				wp_reset_postdata(); ?>
			</ul>
		</section>
<?php
		return ob_get_clean();
	}
}

add_shortcode('related_use_cases', function ($atts = []) {
	return integr8ai_render_related_use_cases($atts);
});
