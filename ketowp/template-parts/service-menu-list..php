<?php
function get_service_menu_list_html()
{
	$services = get_posts([
		'post_type'      => 'service',
		'posts_per_page' => -1,
		'orderby'        => 'menu_order',
		'order'          => 'ASC',
	]);
	if ($services) {
		ob_start();
?>
		<ul class="service-menu-list">
			<?php foreach ($services as $service): ?>
				<li>
					<a href="<?php echo esc_url(get_permalink($service->ID)); ?>">
						<?php echo esc_html(get_the_title($service->ID)); ?>
					</a>
				</li>
			<?php endforeach; ?>
		</ul>
<?php
		return ob_get_clean();
	}
	return '';
}

// echo get_service_menu_list_html();

add_shortcode('service_menu_list', function () {
	return get_service_menu_list_html();
});
