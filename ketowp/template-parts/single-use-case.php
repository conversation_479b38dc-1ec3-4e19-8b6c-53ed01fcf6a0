<?php

/**
 * Renderer: single use case
 */
if (!function_exists('integr8ai_render_single_use_case')) {
	function integr8ai_render_single_use_case()
	{
		global $post;
		if (!isset($post) || 'use_case' !== get_post_type($post)) {
			return '';
		}

		$post_id = $post->ID;
		$title   = get_the_title($post);
		$content = wp_kses_post(apply_filters('the_content', $post->post_content));
		$byline  = get_field('byline', 'option');
		$client  = get_field('client', $post_id);

		ob_start();
?>
		<section class="container single-use-case" aria-labelledby="single-use-case-title">
			<div class="content">
				<h1 id="single-use-case-title"><?php echo esc_html($title); ?></h1>

				<div class="post-content">
					<?php echo $content; ?>
				</div>
			</div>

			<div class="header">
				<?php if ($client): ?>
					<h2 class="client"><?php echo esc_html($client); ?></h2>
				<?php endif; ?>

				<?php echo integr8ai_render_byline($byline); ?>

				<?php if (has_post_thumbnail($post_id)):
					$thumb_id = get_post_thumbnail_id($post_id);
					$img_url  = wp_get_attachment_image_url($thumb_id, 'full');
					$img_alt  = get_post_meta($thumb_id, '_wp_attachment_image_alt', true) ?: $title;
				?>
					<figure class="featured-image">
						<picture>
							<img src="<?php echo esc_url($img_url); ?>" alt="<?php echo esc_attr($img_alt); ?>" loading="lazy" />
						</picture>
					</figure>
				<?php endif; ?>
			</div>
		</section>
<?php
		return ob_get_clean();
	}
}

/**
 * Shortcode: [single_use_case]
 * (for Elementor / editor use)
 */
add_shortcode('single_use_case', function () {
	return integr8ai_render_single_use_case();
});
