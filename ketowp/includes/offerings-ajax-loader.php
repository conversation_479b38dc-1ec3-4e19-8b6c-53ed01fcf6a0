<?php
add_action('wp_ajax_filter_service_offerings', 'filter_service_offerings');
add_action('wp_ajax_nopriv_filter_service_offerings', 'filter_service_offerings');

function filter_service_offerings()
{
	if (empty($_POST['service_id'])) {
		wp_send_json_error('Missing service ID');
	}

	$service_id = intval($_POST['service_id']);
	$term_slug  = sanitize_text_field($_POST['term'] ?? '');

	$args = array(
		'post_type'      => 'service-offering',
		'posts_per_page' => -1,
		'meta_query'     => array(
			array(
				'key'     => 'what_service_does_this_offering_belong_to',
				'value'   => $service_id,
				'compare' => 'LIKE',
			),
		),
	);

	if ($term_slug && $term_slug !== 'all') {
		$args['tax_query'] = array(
			array(
				'taxonomy' => 'service-offering-tag',
				'field'    => 'slug',
				'terms'    => $term_slug,
			),
		);
	}

	$offerings = new WP_Query($args);

	ob_start();
?>
	<ul class="offerings-list">
		<?php if ($offerings->have_posts()) : ?>
			<?php while ($offerings->have_posts()) : $offerings->the_post(); ?>
				<li>
					<article <?php post_class(); ?>>
						<h3><?php the_title(); ?></h3>
						<div class="excerpt"><?php the_excerpt(); ?></div>
					</article>
				</li>
			<?php endwhile;
			wp_reset_postdata(); ?>
		<?php else : ?>
			<li>No offerings found.</li>
		<?php endif; ?>
	</ul>
<?php
	$html = ob_get_clean();

	wp_send_json_success($html);
}

add_action('wp_enqueue_scripts', function () {
	wp_enqueue_script(
		'service-filter',
		get_stylesheet_directory_uri() . '/js/service-filter.js',
		array('jquery'),
		null,
		true
	);

	wp_localize_script('service-filter', 'serviceFilter', array(
		'ajax_url'   => admin_url('admin-ajax.php'),
		'service_id' => is_singular('service') ? get_the_ID() : 0,
	));
});
