<?php
// [homepage_section index="1"]
add_shortcode('homepage_section', function ($atts) {
	$atts = shortcode_atts([
		'index' => 1, // 1-based index
	], $atts, 'homepage_section');

	$sections = get_field('homepage_sections', 'option');
	if (!$sections || !is_array($sections)) {
		return '';
	}

	$index = intval($atts['index']);
	if ($index < 1 || $index > count($sections)) {
		return '';
	}

	// Get the section row
	$row = $sections[$index - 1];

	$byline       = $row['byline'] ?? '';
	$title        = $row['title'] ?? '';
	$description  = $row['description'] ?? '';
	$featured_img = $row['featured_image'] ?? null;
	$cta_text     = $row['cta_text'] ?? '';
	$cta_link     = $row['cta_link'] ?? '';
	$bg_url       = $featured_img && isset($featured_img['url']) ? esc_url($featured_img['url']) : '';

	ob_start(); ?>

	<section class="homepage-section section-<?php echo esc_attr($index); ?>"
		style="background-image: url('<?php echo $bg_url; ?>');">

		<div class="section-inner">
			<?php if ($byline): ?>
				<div class="section-byline">
					<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
						<path d="M10 0L11.4142 8.58579L20 10L11.4142 11.4142L10 20L8.58579 11.4142L0 10L8.58579 8.58579L10 0Z" fill="#07344A"></path>
					</svg>
					<h1><?php echo esc_html($byline); ?></h1>
				</div>
			<?php endif; ?>

			<?php if ($title): ?>
				<h2 class="section-title"><?php echo esc_html($title); ?></h2>
			<?php endif; ?>

			<?php if ($description): ?>
				<div class="section-description">
					<?php echo wp_kses_post($description); ?>
				</div>
			<?php endif; ?>

			<?php if ($cta_text && $cta_link): ?>
				<div class="section-cta">
					<svg xmlns="http://www.w3.org/2000/svg" width="137" height="137" viewBox="0 0 137 137" fill="none">
						<path d="M68.4999 23.3812L74.8808 62.1201L113.62 68.501L74.8808 74.8819L68.4999 113.621L62.119 74.8819L23.3801 68.501L62.119 62.1201L68.4999 23.3812Z" fill="#07344A"></path>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M68.5 135.36C105.425 135.36 135.359 105.426 135.359 68.501C135.359 31.5756 105.425 1.6417 68.5 1.6417C31.5746 1.6417 1.64072 31.5756 1.64072 68.501C1.64072 105.426 31.5746 135.36 68.5 135.36ZM68.5 137.001C106.332 137.001 137 106.332 137 68.501C137 30.6695 106.332 0.000976562 68.5 0.000976562C30.6685 0.000976562 0 30.6695 0 68.501C0 106.332 30.6685 137.001 68.5 137.001Z" fill="#07344A"></path>
					</svg>
					<a href="<?php echo esc_url($cta_link); ?>" class="cta-link">
						<?php echo esc_html($cta_text); ?>
					</a>
				</div>
			<?php endif; ?>
		</div>
	</section>

<?php
	return ob_get_clean();
});
