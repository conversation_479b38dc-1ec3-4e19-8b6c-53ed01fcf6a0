<?php
add_shortcode('mm_children', function ($atts) {
	$atts = shortcode_atts([
		'parent' => 0,
		'limit'  => -1,
	], $atts, 'mm_children');

	$parent_id = intval($atts['parent']);
	if (!$parent_id) {
		error_log('mm_children: no parent ID provided.');
		return '';
	}

	$args = [
		'post_type'      => 'mega-menu',
		'posts_per_page' => intval($atts['limit']),
		'post_parent'    => $parent_id,
		'orderby'        => 'menu_order', // manual sorting order
		'order'          => 'ASC',
		'post_status'    => 'publish',
	];

	$posts = get_posts($args);

	if (empty($posts)) {
		error_log("mm_children: no child posts found for parent=$parent_id.");
		return '';
	}
?>
	<ul class="mm-child-list">
		<?php
		foreach ($posts as $p) {
			// Use alternative_title if available, otherwise fallback to post_title
			$alt_title = get_field('alternative_title', $p->ID);
			$title     = $alt_title ? $alt_title : $p->post_title;
		?>
			<li data-id="<?php echo (int) $p->ID; ?>" class="mm-child-item"><span><?php echo esc_html($title); ?></span></li>
		<?php
		}
		?>
	</ul>
<?php
});
