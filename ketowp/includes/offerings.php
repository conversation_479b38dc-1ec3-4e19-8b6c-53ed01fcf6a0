<?php
add_shortcode('service_offerings', function () {
	ob_start();

	if (! is_singular('service')) {
		return ob_get_clean();
	}

	$service_id = get_the_ID();
	$term_slug  = isset($_GET['service_term']) ? sanitize_text_field($_GET['service_term']) : '';

	$args = array(
		'post_type'      => 'service-offering',
		'posts_per_page' => -1,
		'meta_query'     => array(
			array(
				'key'     => 'what_service_does_this_offering_belong_to',
				'value'   => $service_id,
				'compare' => 'LIKE',
			),
		),
	);

	if ($term_slug && $term_slug !== 'all') {
		$args['tax_query'] = array(
			array(
				'taxonomy' => 'service-offering-tag',
				'field'    => 'slug',
				'terms'    => $term_slug,
			),
		);
	}

	$offerings = new WP_Query($args);

?>
	<ul class="offerings-list">
		<?php if ($offerings->have_posts()) : ?>
			<?php while ($offerings->have_posts()) : $offerings->the_post(); ?>
				<li>
					<article <?php post_class(); ?>>
						<h3><?php the_title(); ?></h3>
						<div class="excerpt"><?php the_excerpt(); ?></div>
					</article>
				</li>
			<?php endwhile;
			wp_reset_postdata(); ?>
		<?php else : ?>
			<li>No offerings found.</li>
		<?php endif; ?>
	</ul>
<?php

	return ob_get_clean();
});
