<?php

/**
 * Get inline SVG from an ACF image field (returns empty string if not valid).
 *
 * @param string $field_name The ACF field name.
 * @param string|int $context Where the field is stored: post ID, term ID, or 'option'.
 * @return string Inline SVG contents or '' if not found.
 */
function integr8ai_get_svg_from_acf($field_name, $context = 'option')
{
	$image_id = get_field($field_name, $context);
	if (! $image_id) {
		return '';
	}

	$file_path = get_attached_file($image_id);
	if (! $file_path || ! file_exists($file_path)) {
		return '';
	}

	// Only allow .svg files
	$ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
	if ($ext !== 'svg') {
		return '';
	}

	$svg = file_get_contents($file_path);

	// Optional: sanitize the SVG (basic strip of <script> tags)
	$svg = preg_replace('#<script(.*?)</script>#is', '', $svg);

	return $svg ? $svg : '';
}
