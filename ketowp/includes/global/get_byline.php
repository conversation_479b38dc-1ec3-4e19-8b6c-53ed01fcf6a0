<?php
if (!function_exists('integr8ai_render_byline')) {
	/**
	 * Render a byline with optional SVG icon.
	 *
	 * @param string $text  Byline text
	 * @param string $icon_field ACF field name for the SVG icon (default: 'byline_icon')
	 * @param string $context Where to fetch the field (default: 'option')
	 * @return string Safe HTML for byline
	 */
	function integr8ai_render_byline($text, $icon_field = 'byline_icon', $context = 'option')
	{
		if (empty($text)) {
			return '';
		}

		$icon_svg = function_exists('integr8ai_get_svg_from_acf')
			? integr8ai_get_svg_from_acf($icon_field, $context)
			: '';

		ob_start();
?>
		<div class="byline">
			<?php if ($icon_svg): ?>
				<span class="icon"><?php echo $icon_svg; ?></span>
			<?php endif; ?>
			<h3><?php echo esc_html($text); ?></h3>
		</div>
<?php
		return ob_get_clean();
	}
}
