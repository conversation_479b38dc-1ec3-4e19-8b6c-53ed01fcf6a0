<?php

/**
 * Safely render a section only if it has rows (flexible content or repeater)
 *
 * @param string   $field_name  The ACF field name
 * @param int|null $post_id     Post ID (defaults to current global post)
 * @param callable $renderer    Callback that echoes the section HTML
 * <AUTHOR> A.
 */
function integr8ai_render_section($field_name, $post_id = null, callable $renderer)
{
	if (have_rows($field_name, $post_id)) {
		ob_start();
		$renderer($post_id);
		$output = ob_get_clean();

		if (!empty(trim($output))) {
			echo $output;
		}
	}
}
