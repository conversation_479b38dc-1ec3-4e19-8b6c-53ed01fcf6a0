<?php
// Register Global Settings Menu
add_action('admin_menu', function () {
	add_menu_page(
		'Global Settings',
		'Global Settings',
		'manage_options',
		'global-settings',
		'render_global_settings_dashboard',
		'dashicons-admin-generic',
		2
	);
});

// Render Global Settings Dashboard
function render_global_settings_dashboard()
{
	global $submenu, $menu;

?>
	<div class="wrap" style="max-width:900px;margin:2rem auto;font-family:sans-serif;">
		<h1 style="font-size:2rem;margin-bottom:1rem;">⚙️ Global Settings</h1>
		<p style="font-size:1.1rem;color:#555;margin-bottom:2rem;">
			This dashboard provides access to all global configuration option pages for your site.
		</p>

		<div style="display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:1rem;">
			<?php
			if (isset($submenu['global-settings'])) {
				foreach ($submenu['global-settings'] as $item) {
					// Skip the parent menu itself
					if ($item[2] === 'global-settings') continue;

					$title = esc_html($item[0]);
					$slug  = $item[2];
					$url   = esc_url(admin_url('admin.php?page=' . $slug));

					// Try to detect icon for submenu from the $menu array
					$icon = 'dashicons-admin-generic'; // default
					foreach ($menu as $m) {
						if ($m[2] === $slug) {
							$icon = !empty($m[6]) ? $m[6] : $icon;
							break;
						}
					}
			?>
					<a href="<?php echo $url; ?>"
						style="display:block;padding:1.2rem;background:#fff;border:1px solid #ddd;
                              border-radius:10px;text-decoration:none;box-shadow:0 2px 6px rgba(0,0,0,0.05);
                              transition:all 0.2s ease;display:flex;align-items:center;gap:0.8rem;">
						<span class="dashicons <?php echo esc_attr($icon); ?>" style="font-size:24px;color:#0073aa;"></span>
						<div>
							<h2 style="font-size:1.2rem;margin:0 0 0.5rem;color:#333;"><?php echo $title; ?></h2>
							<p style="margin:0;color:#666;font-size:0.9rem;">Manage <?php echo strtolower($title); ?> options</p>
						</div>
					</a>
			<?php
				}
			} else {
				echo '<p>No global option pages have been registered yet.</p>';
			}
			?>
		</div>
	</div>
<?php
}
