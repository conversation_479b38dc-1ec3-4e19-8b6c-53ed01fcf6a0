<?php
// Minimal: register a nav location and provide a shortcode [legal_menu]
add_action('init', function () {
	if (function_exists('register_nav_menu')) {
		register_nav_menu('legal_menu', 'Legal Menu');
	}
});

add_shortcode('legal_menu', function () {
	if (! function_exists('has_nav_menu') || ! has_nav_menu('legal_menu')) {
		return '';
	}
	return (string) wp_nav_menu(array(
		'theme_location' => 'legal_menu',
		'container' => '',
		'items_wrap' => '<ul class="legal-items">%3$s</ul>',
		'fallback_cb' => false,
		'echo' => false,
	));
});
