<?php
add_shortcode('mm_parents', function ($atts) {
	$atts = shortcode_atts([
		'limit' => -1,
	], $atts, 'mm_parents');

	$args = [
		'post_type' => 'mega-menu',
		'posts_per_page' => intval($atts['limit']),
		'post_parent' => 0, // only top-level items
		'orderby' => 'menu_order', // order by manual sorting
		'order' => 'ASC',
		'post_status' => 'publish',
	];

	$posts = get_posts($args);

	if (empty($posts)) {
		error_log('mm_parents: no parent posts found for post_type=mega-menu.');
		return '';
	}
?>
	<ul class="mm-parent-list">
		<?php
		foreach ($posts as $p) {
		?>
			<li data-id="<?php echo (int) $p->ID; ?>" class="mm-parent-item"><?php echo esc_html($p->post_title); ?></li>
		<?php
		}
		?>
	</ul>
<?php
});
