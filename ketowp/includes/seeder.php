<?php

/**
 * Plugin Name: Demo Seeder
 * Description: Seeds Services and Use Cases with ACF sections.
 */

if (!defined('ABSPATH')) exit;

class DemoSeeder
{

	public function __construct()
	{
		add_action('admin_menu', [$this, 'add_menu']);
	}

	public function add_menu()
	{
		add_menu_page('Seed Demo', 'Seed Demo', 'manage_options', 'seed-demo', [$this, 'render_page'], 'dashicons-database-import');
	}

	public function render_page()
	{
		if (isset($_POST['seed_demo'])) {
			$this->seed();
		}

		echo '<div class="wrap"><h1>Seed Demo</h1>';
		echo '<form method="post">';
		submit_button('Run Seeder', 'primary', 'seed_demo');
		echo '</form></div>';
	}

	private function seed()
	{
		// Flush old data
		$this->delete_posts('service');
		$this->delete_posts('use_case');

		// Create Use Cases
		$use_cases = [
			['title' => 'AI-Powered Chatbots', 'content' => 'Deploy conversational AI assistants that scale with your business.'],
			['title' => 'Fraud Detection', 'content' => 'Leverage machine learning to detect fraudulent transactions in real-time.'],
			['title' => 'Predictive Analytics', 'content' => 'Use historical data to predict future trends and customer behavior.'],
			['title' => 'Recommendation Engines', 'content' => 'Deliver personalized content and product suggestions to users.'],
		];
		$uc_ids = [];
		foreach ($use_cases as $uc) {
			$uc_ids[] = wp_insert_post([
				'post_type' => 'use_case',
				'post_status' => 'publish',
				'post_title' => $uc['title'],
				'post_content' => $uc['content'],
			]);
		}

		// Create Services with Sections
		$services = [
			['title' => 'Cloud Infrastructure', 'content' => 'Enterprise-grade cloud services for scaling applications.'],
			['title' => 'Data Engineering', 'content' => 'Robust pipelines for transforming raw data into insights.'],
			['title' => 'Cybersecurity Solutions', 'content' => 'Protect your digital assets with next-gen security systems.'],
			['title' => 'AI Consulting', 'content' => 'Tailored strategies to integrate AI into your organization.'],
		];

		foreach ($services as $service) {
			$id = wp_insert_post([
				'post_type' => 'service',
				'post_status' => 'publish',
				'post_title' => $service['title'],
				'post_content' => $service['content'],
			]);

			if ($id && !is_wp_error($id)) {
				// Build sections (flexible content)
				$sections = [];

				// Hero
				$sections[] = [
					'acf_fc_layout' => 'hero',
					'byline' => 'Kickstart your journey with ' . $service['title'],
					'cta_text' => 'Learn More',
					'cta_link' => ['url' => '#', 'title' => 'Learn More', 'target' => ''],
				];

				// Benefits
				$sections[] = [
					'acf_fc_layout' => 'benefits',
					'title' => 'Why Choose ' . $service['title'],
					'byline' => 'Core benefits that drive value',
					'benefits_list' => [
						['title' => 'Scalable', 'description' => 'Easily adapts as your business grows.'],
						['title' => 'Secure', 'description' => 'Built with enterprise-grade security.'],
						['title' => 'Reliable', 'description' => '24/7 uptime with global support.'],
					]
				];

				// Offerings
				$sections[] = [
					'acf_fc_layout' => 'offerings',
					'title' => 'Our Offerings',
					'byline' => 'Comprehensive solutions for every need',
					'filters' => [
						['filter_label' => 'Enterprise', 'filter_key' => 'enterprise'],
						['filter_label' => 'SMB', 'filter_key' => 'smb'],
					],
					'offerings_list' => [
						['title' => 'Starter Plan', 'description' => 'Perfect for small teams.', 'filter_key' => 'smb'],
						['title' => 'Enterprise Suite', 'description' => 'Scalable solutions for large organizations.', 'filter_key' => 'enterprise'],
					]
				];

				// Process
				$sections[] = [
					'acf_fc_layout' => 'process',
					'title' => 'Our Process',
					'byline' => 'A clear path to success',
					'intro' => 'We follow a structured methodology to ensure consistent delivery.',
					'steps' => [
						['step_title' => 'Discovery', 'step_description' => 'Understand your business needs.'],
						['step_title' => 'Design', 'step_description' => 'Craft tailored solutions.'],
						['step_title' => 'Deployment', 'step_description' => 'Implement with minimal downtime.'],
						['step_title' => 'Optimization', 'step_description' => 'Continuous improvements post-launch.'],
					]
				];

				// Use Cases (relation)
				$sections[] = [
					'acf_fc_layout' => 'use_cases',
					'title' => 'Related Use Cases',
					'byline' => 'Explore how others use ' . $service['title'],
					'use_cases' => array_slice($uc_ids, 0, 2),
				];

				// Save to ACF
				update_field('field_service_sections', $sections, $id);
			}
		}

		// Success Output
		$svc_posts = get_posts(['post_type' => 'service', 'numberposts' => -1]);
		$uc_posts = get_posts(['post_type' => 'use_case', 'numberposts' => -1]);

		echo '<div class="updated"><p>';
		echo count($svc_posts) . " Services created: " . implode(', ', wp_list_pluck($svc_posts, 'post_title')) . "<br>";
		echo count($uc_posts) . " Use Cases created: " . implode(', ', wp_list_pluck($uc_posts, 'post_title'));
		echo '</p></div>';
	}

	private function delete_posts($type)
	{
		$old = get_posts(['post_type' => $type, 'numberposts' => -1, 'post_status' => 'any']);
		foreach ($old as $p) {
			wp_delete_post($p->ID, true);
		}
	}
}

new DemoSeeder();
