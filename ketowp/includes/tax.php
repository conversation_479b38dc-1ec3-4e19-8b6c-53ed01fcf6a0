<?php

add_shortcode('service_offering_pills', function () {
    ob_start();

    if (! is_singular('service')) {
        return ob_get_clean();
    }

    $service_id = get_the_ID();

    $offerings = get_posts(array(
        'post_type'      => 'service-offering',
        'posts_per_page' => -1,
        'fields'         => 'ids',
        'meta_query'     => array(
            array(
                'key'     => 'what_service_does_this_offering_belong_to',
                'value'   => $service_id,
                'compare' => 'LIKE',
            ),
        ),
    ));

    if (! $offerings) {
        return ob_get_clean();
    }

    $terms = wp_get_object_terms($offerings, 'service-offering-tag');

?>
    <ul class="offering-pills">
        <li><a href="#" data-term="all">All</a></li>
        <?php if (! empty($terms) && ! is_wp_error($terms)) : ?>
            <?php foreach ($terms as $term) : ?>
                <li>
                    <a href="#" data-term="<?php echo esc_attr($term->slug); ?>">
                        <?php echo esc_html($term->name); ?>
                    </a>
                </li>
            <?php endforeach; ?>
        <?php endif; ?>
    </ul>
<?php

    return ob_get_clean();
});
