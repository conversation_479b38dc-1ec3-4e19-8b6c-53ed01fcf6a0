<?php

add_shortcode('service_extra', function ($atts) {
	if (!is_singular('service')) {
		return '';
	}

	global $post;

	ob_start();

	integr8ai_render_section('service_sections', $post->ID, function ($post_id) {
		while (have_rows('service_sections', $post_id)) : the_row();
			if (get_row_layout() !== 'extra') {
				continue;
			}

			$title       = get_sub_field('title');
			$byline      = get_sub_field('byline');
			$description = get_sub_field('description');
			$bg_image    = get_sub_field('bg_image');
			$extra_list  = get_sub_field('extra_list');
?>

			<section class="container service-extra" aria-labelledby="service-extra-title">
				<div class="wrapper">


					<?php if (!empty($title)) : ?>
						<h2 id="service-extra-title"><?php echo esc_html($title); ?></h2>
					<?php endif; ?>

					<?php echo integr8ai_render_byline($byline); ?>

					<?php if (!empty($description)) : ?>
						<p class="extra-description">
							<?php echo wp_kses_post($description); ?>
						</p>
					<?php endif; ?>

					<?php if (!empty($bg_image)) :
						$image_url = wp_get_attachment_image_url($bg_image, 'full');
						$image_alt = get_post_meta($bg_image, '_wp_attachment_image_alt', true);
					?>
						<figure class="extra-bg-image">
							<picture>
								<img src="<?php echo esc_url($image_url); ?>"
									alt="<?php echo esc_attr($image_alt); ?>"
									loading="lazy" />
							</picture>
						</figure>
					<?php endif; ?>

					<?php if (!empty($extra_list)) : ?>
						<ul class="extra-list">
							<?php foreach ($extra_list as $item) : ?>
								<li class="extra-item">
									<article class="extra-card">
										<?php if (!empty($item['title'])) : ?>
											<h4><?php echo esc_html($item['title']); ?></h4>
										<?php endif; ?>

										<?php if (!empty($item['description'])) : ?>
											<p><?php echo esc_html($item['description']); ?></p>
										<?php endif; ?>

										<?php if (!empty($item['cta']) && is_array($item['cta'])) : ?>
											<a href="<?php echo esc_url($item['cta']['url']); ?>"
												target="<?php echo esc_attr($item['cta']['target']); ?>"
												class="btn">
												<?php echo esc_html($item['cta']['title']); ?>
											</a>
										<?php endif; ?>
									</article>
								</li>
							<?php endforeach; ?>
						</ul>
					<?php endif; ?>
				</div>
			</section>
<?php
		endwhile;
	});

	return ob_get_clean();
});
