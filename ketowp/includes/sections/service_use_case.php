<?php
add_shortcode('service_use_cases', function ($atts) {
	if (!is_singular('service')) {
		return '';
	}

	ob_start();

	if (have_rows('service_sections')) :
		while (have_rows('service_sections')) : the_row();
			if (get_row_layout() === 'use_cases') :

				$title  = get_sub_field('title');
				$byline = get_sub_field('byline');

				// Options (Services Settings)
				$byline_icon_svg = function_exists('integr8ai_get_svg_from_acf')
					? integr8ai_get_svg_from_acf('byline_icon', 'option')
					: '';
				$arrow_icon_svg = function_exists('integr8ai_get_svg_from_acf')
					? integr8ai_get_svg_from_acf('arrow_icon', 'option')
					: '';

				// Query first 3 published use cases, ASC
				$use_cases = get_posts([
					'post_type'      => 'use_case',
					'posts_per_page' => 3,
					'orderby'        => 'menu_order',
					'order'          => 'ASC',
				]);
?>

				<section class="container service-use-cases" aria-labelledby="service-use-cases-title">
					<?php if ($title): ?>
						<h2 id="service-use-cases-title"><?php echo esc_html($title); ?></h2>
					<?php endif; ?>

					<?php if ($byline): ?>
						<div class="byline">
							<?php if ($byline_icon_svg): ?>
								<div class="icon"><?php echo $byline_icon_svg; ?></div>
							<?php endif; ?>
							<h3><?php echo esc_html($byline); ?></h3>
						</div>
					<?php endif; ?>

					<?php if ($use_cases): ?>
						<ul class="service-use-cases__list">
							<?php foreach ($use_cases as $uc):
								$title = get_field('card_title', $uc->ID) ?: get_the_title($uc->ID);
								$client = get_field('client', $uc->ID);
								$thumb_id = get_post_thumbnail_id($uc->ID);
								$thumb = $thumb_id ? wp_get_attachment_image_url($thumb_id, 'full') : false;
							?>
								<li class="use-case-card">
									<article>
										<div class="content">
											<div>

												<h4>
													<?php echo esc_html($title); ?>
												</h4>
												<a href="<?php echo esc_url(get_permalink($uc->ID)); ?>">
													<span>
														Learn more
													</span>
													<?php if ($arrow_icon_svg): ?>
														<span class="icon"><?php echo $arrow_icon_svg; ?></span>
													<?php endif; ?>
												</a>

											</div>
											<?php if (!empty($client)): ?>
												<span class="client"><?php echo esc_html($client); ?></span>
											<?php endif; ?>
										</div>


										<figure>
											<picture>
												<?php if ($thumb): ?>
													<img src="<?php echo esc_url($thumb); ?>" alt="<?php echo esc_attr(get_the_title($uc->ID)); ?>" loading="lazy" />
												<?php endif; ?>
											</picture>
										</figure>
									</article>
								</li>
							<?php endforeach; ?>
						</ul>
					<?php endif; ?>
				</section>

<?php
			endif;
		endwhile;
	endif;

	return ob_get_clean();
});
