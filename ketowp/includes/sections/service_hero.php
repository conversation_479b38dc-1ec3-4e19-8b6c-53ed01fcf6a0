<?php
add_shortcode('service_hero', function ($atts) {
	if (!is_singular('service')) {
		return '';
	}

	global $post;

	ob_start();

	integr8ai_render_section('service_sections', $post->ID, function ($post_id) {
		while (have_rows('service_sections', $post_id)) : the_row();
			if (get_row_layout() !== 'hero') {
				continue;
			}

			// Fields
			$byline    = get_sub_field('byline');
			$cta_link  = get_sub_field('cta_link');

			// Featured image
			$thumb_id  = get_post_thumbnail_id($post_id);
			$thumb_alt = get_post_meta($thumb_id, '_wp_attachment_image_alt', true);
			if (empty($thumb_alt)) {
				$thumb_alt = get_the_title($post_id);
			}

			// CTA icon (Services Settings)
			$cta_icon_svg = function_exists('integra8ai_get_svg_from_acf')
				? integr8ai_get_svg_from_acf('here_cta_icon', 'option')
				: '';
?>

			<section class="container service-hero" aria-labelledby="service-hero-title">
				<h1 id="service-hero-title"><?php echo esc_html(get_the_title($post_id)); ?></h1>

				<?php echo integr8ai_render_byline($byline); ?>

				<div class="cta-box">
					<?php if (!empty($thumb_id)) :
						$img_url = wp_get_attachment_image_url($thumb_id, 'full');
					?>
						<figure>
							<picture>
								<img src="<?php echo esc_url($img_url); ?>"
									alt="<?php echo esc_attr($thumb_alt); ?>"
									loading="lazy" />
							</picture>
						</figure>
					<?php endif; ?>

					<?php if (!empty($cta_link['url']) && !empty($cta_text)) : ?>
						<div class="cta">
							<?php if (!empty($cta_icon_svg)) : ?>
								<div class="icon"><?php echo $cta_icon_svg; ?></div>
							<?php endif; ?>
							<a href="<?php echo esc_url($cta_link['url']); ?>">
								<?php echo esc_html($cta_link['title']); ?>
							</a>
						</div>
					<?php endif; ?>
				</div>

				<?php if (get_post_field('post_content', $post_id)) : ?>
					<p><?php echo wp_kses_post(get_the_content(null, false, $post_id)); ?></p>
				<?php endif; ?>
			</section>
<?php
		endwhile;
	});

	return ob_get_clean();
});
