<?php
add_shortcode('service_methods', function ($atts) {
	if (!is_singular('service')) {
		return '';
	}

	global $post;

	ob_start();

	integr8ai_render_section('service_sections', $post->ID, function ($post_id) {
		while (have_rows('service_sections', $post_id)) : the_row();
			if (get_row_layout() !== 'methods') {
				continue;
			}

			$title  = get_sub_field('title');
			$byline = get_sub_field('byline');
			$items  = get_sub_field('methodology_list');
?>
			<section class="container service-methods" aria-labelledby="service-methods-title">
				<?php if ($title): ?>
					<h2 id="service-methods-title"><?php echo esc_html($title); ?></h2>
				<?php endif; ?>

				<?php echo integr8ai_render_byline($byline); ?>

				<?php if ($items): ?>
					<ul>
						<?php foreach ($items as $item):
							$bg_id = $item['bg_image'];
							$bg_url = $bg_id ? wp_get_attachment_image_url($bg_id, 'large') : '';
						?>
							<li class="method" <?php echo $bg_url ? ' style="background-image:url(' . esc_url($bg_url) . ')"' : ''; ?>>
								<div class="wrapper">
									<article>
										<?php if ($item['title']): ?>
											<h3><?php echo esc_html($item['title']); ?></h3>
										<?php endif; ?>
										<?php if ($item['description']): ?>
											<p><?php echo esc_html($item['description']); ?></p>
										<?php endif; ?>
									</article>
								</div>
							</li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</section>
<?php
		endwhile;
	});

	return ob_get_clean();
});
