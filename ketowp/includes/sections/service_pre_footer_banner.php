<?php

function shortcode_pre_footer_banner()
{
	// Get ACF fields from the Prefooter Settings options page
	$title       = get_field('title', 'option');
	$description = get_field('description', 'option');
	$cta_link    = get_field('cta_link', 'option');
	$image_id    = get_field('banner_image', 'option');
	$arrow_icon_svg = function_exists('integr8ai_get_svg_from_acf')
		? integr8ai_get_svg_from_acf('arrow_icon', 'option')
		: '';

	if (!$title && !$description  && !$cta_link && !$image_id) {
		return '';
	}

	$image_url = $image_id ? wp_get_attachment_url($image_id) : '';

	ob_start(); ?>
	<section class="container pre-footer-banner">


		<div class="banner-content">
			<div class="text">
				<?php if ($title): ?>
					<h2><?php echo esc_html($title); ?></h2>
				<?php endif; ?>

				<?php if ($description): ?>
					<p><?php echo esc_html($description); ?></p>
				<?php endif; ?>
			</div>

			<?php if ($cta_link && isset($cta_link['url'])): ?>
				<a href="<?php echo esc_url($cta_link['url']); ?>"
					<?php echo isset($cta_link['target']) && $cta_link['target'] ? 'target="' . esc_attr($cta_link['target']) . '"' : ''; ?>
					class="cta-button">
					<span><?php echo esc_html($cta_link['title']); ?></span>
					<?php if ($arrow_icon_svg): ?>
						<span aria-hidden="true"><?php echo $arrow_icon_svg; ?></span>
					<?php endif; ?>
				</a>
			<?php endif; ?>

			<?php if ($image_url): ?>
				<figure class="banner-bg-image">
					<picture>
						<?php if ($image_url): ?>
							<img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($title); ?>" loading="lazy" class="banner-bg-img" />
						<?php endif; ?>
					</picture>
				</figure>
			<?php endif; ?>
		</div>


	</section>
<?php
	return ob_get_clean();
}
add_shortcode('pre_footer_banner', 'shortcode_pre_footer_banner');
?>