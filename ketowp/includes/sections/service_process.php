<?php
add_shortcode('service_process', function ($atts) {
	ob_start();

	// Flexible content (inside a Service post)
	if (have_rows('service_sections')) :
		while (have_rows('service_sections')) : the_row();
			if (get_row_layout() === 'process') :

				$title       = get_sub_field('title');
				$byline      = get_sub_field('byline');
				$description = get_sub_field('description');
				$steps       = get_sub_field('steps');

				// Options (Services Settings)
				$process_icon_svg  = function_exists('integr8ai_get_svg_from_acf') ? integr8ai_get_svg_from_acf('process_icon', 'option') : '';
				$process_logo = get_field('process_logo', 'option');
				$tagline      = get_field('process_tagline', 'option');
				$bg_image     = get_field('process_image_background', 'option');

?>
				<section class="container service-process" aria-labelledby="service-process-title">
					<?php if ($title): ?>
						<h2 id="service-process-title"><?php echo esc_html($title); ?></h2>
					<?php endif; ?>

					<?php echo integr8ai_render_byline($byline); ?>


					<?php if ($description): ?>
						<p class="intro"><?php echo esc_html($description); ?></p>
					<?php endif; ?>

					<div class="service-process__grid">
						<div class="service-process__banner">
							<?php if ($bg_image): ?>
								<figure class="banner-bg-image">
									<picture>
										<img
											src="<?php echo esc_url($bg_image['url'] ?? ''); ?>"
											alt="<?php echo esc_attr($bg_image['alt'] ?? ''); ?>"
											loading="lazy"
											class="banner-bg-img" />
									</picture>
								</figure>
							<?php endif; ?>

							<?php if ($process_logo): ?>
								<figure class="banner-logo">
									<picture>
										<img src="<?php echo esc_url($process_logo['url'] ?? ''); ?>"
											alt="<?php echo esc_attr($process_logo['alt'] ?? ''); ?>"
											loading="lazy" />
									</picture>
								</figure>
							<?php endif; ?>


							<?php if ($tagline): ?>
								<div class="banner-tagline">
									<?php echo integr8ai_render_byline($byline); ?>
									<h4><?php echo esc_html($tagline); ?></h4>
								</div>
							<?php endif; ?>
						</div>

						<div class="service-process__list">
							<h3><?php echo esc_html($title); ?></h3>
							<?php if ($steps): ?>
								<ul>
									<?php foreach ($steps as $step): ?>
										<li>
											<?php echo $process_icon_svg ?>
											<span><?php echo esc_html($step['step_title']); ?></span>
										</li>
									<?php endforeach; ?>
								</ul>
							<?php endif; ?>
						</div>
					</div>
				</section>
<?php

			endif;
		endwhile;
	endif;

	return ob_get_clean();
});
