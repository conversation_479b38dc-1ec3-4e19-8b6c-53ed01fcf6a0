<?php
add_shortcode('service_offerings', function ($atts) {
	if (!is_singular('service')) {
		return '';
	}

	global $post;

	ob_start();

	integr8ai_render_section('service_sections', $post->ID, function ($post_id) {
		while (have_rows('service_sections', $post_id)) : the_row();
			if (get_row_layout() !== 'offerings') {
				continue;
			}

			$title  = get_sub_field('title');
			$byline = get_sub_field('byline');
			$cards  = get_sub_field('offerings_list');

			if (empty($cards)) {
				continue;
			}

			// Collect unique categories
			$categories = [];
			foreach ($cards as $card) {
				if (!empty($card['category'])) {
					$cat_id = is_array($card['category']) ? $card['category'][0] : $card['category'];
					$term   = get_term($cat_id);
					if ($term) {
						$categories[$term->term_id] = $term->name;
					}
				}
			}

			// Icons from Services Settings
			$offer_icon_svg = integr8ai_get_svg_from_acf('offer_icon', 'option');
?>

			<section class="container service-offerings" aria-labelledby="service-offerings-title">
				<?php if (!empty($title)) : ?>
					<h2 id="service-offerings-title"><?php echo esc_html($title); ?></h2>
				<?php endif; ?>

				<?php echo integr8ai_render_byline($byline); ?>

				<?php if (!empty($categories)) : ?>
					<?php $first_cat_id = key($categories); ?>
					<div class="service-offerings__filters-wrapper">
						<ul class="service-offerings__filters" role="tablist">
							<?php foreach ($categories as $cat_id => $cat_name) : ?>
								<li role="presentation">
									<button type="button" role="tab"
										data-filter="<?php echo esc_attr($cat_id); ?>"
										<?php echo ($cat_id === $first_cat_id) ? 'aria-selected="true" class="active"' : ''; ?>>
										<span><?php echo esc_html($cat_name); ?></span>
									</button>
								</li>
							<?php endforeach; ?>
						</ul>

						<div class="service-offerings__active-filter">
							<h4 id="active-filter-label"><?php echo esc_html($categories[$first_cat_id]); ?></h4>
						</div>
					</div>
				<?php endif; ?>

				<ul class="service-offerings__list">
					<?php foreach ($cards as $card) :
						$card_title = isset($card['title']) ? esc_html($card['title']) : '';
						$card_desc  = isset($card['description']) ? esc_html($card['description']) : '';
						$card_cat   = !empty($card['category']) ? (is_array($card['category']) ? $card['category'][0] : $card['category']) : '';
					?>
						<li class="offering-card
							<?php
							if (!empty($categories)) {
								echo ($card_cat === $first_cat_id) ? '' : 'hidden';
							}
							?>"
							<?php if (!empty($categories)) : ?>
							data-filter-group="<?php echo esc_attr($card_cat); ?>"
							<?php endif; ?>>
							<article>
								<?php if (!empty($offer_icon_svg)) : ?>
									<div class="icon"><?php echo $offer_icon_svg; ?></div>
								<?php endif; ?>
								<h4><?php echo $card_title; ?></h4>
								<p><?php echo $card_desc; ?></p>
							</article>
						</li>
					<?php endforeach; ?>
				</ul>
			</section>
<?php
		endwhile;
	});

	return ob_get_clean();
});
