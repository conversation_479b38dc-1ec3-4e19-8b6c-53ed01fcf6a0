<?php

add_shortcode('service_benefits', function ($atts) {
	if (!is_singular('service')) {
		return '';
	}

	global $post;

	ob_start();

	integr8ai_render_section('service_sections', $post->ID, function ($post_id) {
		while (have_rows('service_sections', $post_id)) : the_row();
			if (get_row_layout() !== 'benefits') {
				continue;
			}

			$title  = get_sub_field('title');
			$byline = get_sub_field('byline');
			$cards  = get_sub_field('benefits_list');

			// Options (Services Settings)
			$benefit_icon_svg = function_exists('integr8ai_get_svg_from_acf')
				? integr8ai_get_svg_from_acf('benefit_icon', 'option')
				: '';
?>

			<section class="container service-benefits" aria-labelledby="service-benefits-title">
				<?php if (!empty($title)) : ?>
					<h2 id="service-benefits-title">
						<?php echo esc_html($title); ?>
					</h2>
				<?php endif; ?>

				<?php echo integr8ai_render_byline($byline); ?>

				<?php if (!empty($cards)) : ?>
					<ul class="benefits-list">
						<?php foreach ($cards as $card) : ?>
							<li class="benefit">
								<article class="benefit-card">
									<?php if (!empty($benefit_icon_svg)) : ?>
										<div class="icon">
											<?php echo $benefit_icon_svg; ?>
										</div>
									<?php endif; ?>

									<?php if (!empty($card['title'])) : ?>
										<h4><?php echo esc_html($card['title']); ?></h4>
									<?php endif; ?>

									<?php if (!empty($card['description'])) : ?>
										<p><?php echo esc_html($card['description']); ?></p>
									<?php endif; ?>
								</article>
							</li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</section>
<?php
		endwhile;
	});

	return ob_get_clean();
});
