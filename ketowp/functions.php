<?php
add_theme_support('post-thumbnails');
add_theme_support('title-tag');

// Enqueue styles and scripts
function my_theme_enqueue()
{
    wp_enqueue_style(
        "ketowp-style",
        get_stylesheet_directory_uri() . "/dist/assets.css",
        filemtime(get_template_directory() . "/dist/assets.css"),
        "all",
    );
    wp_enqueue_script(
        'ketowp-script',
        get_template_directory_uri() . '/dist/main.js',
        [],
        "all",
        true
    );
}
add_action('wp_enqueue_scripts', 'my_theme_enqueue');

// ACF JSON sync
add_filter('acf/settings/save_json', function ($path) {
    return get_stylesheet_directory() . '/acf-json';
});

add_filter('acf/settings/load_json', function ($paths) {
    unset($paths[0]);
    $paths[] = get_stylesheet_directory() . '/acf-json';
    return $paths;
});
